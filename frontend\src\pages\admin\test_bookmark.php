<?php
// Test bookmark functionality
require_once 'config.php';

header('Content-Type: application/json');

try {
    // Test if we have any posts to test with
    $pdo = getConnection();
    $stmt = $pdo->query("SELECT * FROM posts LIMIT 1");
    $testPost = $stmt->fetch();
    
    if (!$testPost) {
        echo "⚠ No posts found in database to test with\n";
        exit;
    }
    
    echo "✓ Found test post: ID " . $testPost['id'] . " - " . $testPost['title'] . "\n";
    
    // Test addSavedNews function
    echo "\nTesting addSavedNews...\n";
    $result = addSavedNews($testPost['id']);
    if ($result) {
        echo "✓ addSavedNews test successful\n";
    } else {
        echo "✗ addSavedNews test failed\n";
    }
    
    // Test getSavedNews function
    echo "\nTesting getSavedNews...\n";
    $savedNews = getSavedNews();
    echo "✓ getSavedNews returned " . count($savedNews) . " saved news items\n";
    
    // Test removeSavedNews function
    echo "\nTesting removeSavedNews...\n";
    $result = removeSavedNews($testPost['id']);
    if ($result) {
        echo "✓ removeSavedNews test successful\n";
    } else {
        echo "✗ removeSavedNews test failed\n";
    }
    
    // Test getSavedNews again to confirm removal
    echo "\nTesting getSavedNews after removal...\n";
    $savedNews = getSavedNews();
    echo "✓ getSavedNews returned " . count($savedNews) . " saved news items after removal\n";
    
    echo "\n✓ All bookmark tests completed!\n";
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
