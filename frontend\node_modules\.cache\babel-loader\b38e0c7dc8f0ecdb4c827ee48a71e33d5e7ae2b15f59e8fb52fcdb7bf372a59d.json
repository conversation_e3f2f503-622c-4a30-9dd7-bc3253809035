{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\pages\\\\user\\\\LandingPage.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport Drawer from '@mui/material/Drawer';\nimport AppBar from '@mui/material/AppBar';\nimport Toolbar from '@mui/material/Toolbar';\nimport Typography from '@mui/material/Typography';\nimport Box from '@mui/material/Box';\nimport Button from '@mui/material/Button';\nimport Chip from '@mui/material/Chip';\nimport Stack from '@mui/material/Stack';\nimport Card from '@mui/material/Card';\nimport CardContent from '@mui/material/CardContent';\nimport CardMedia from '@mui/material/CardMedia';\nimport Avatar from '@mui/material/Avatar';\nimport Divider from '@mui/material/Divider';\nimport MenuIcon from '@mui/icons-material/Menu';\nimport Footer from './components/Footer';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme } from '@mui/material/styles';\nimport ShareIcon from '@mui/icons-material/Share';\nimport BookmarkAddIcon from '@mui/icons-material/BookmarkAdd';\nimport BookmarkAddedIcon from '@mui/icons-material/BookmarkAdded';\nimport Dialog from '@mui/material/Dialog';\nimport IconButton from '@mui/material/IconButton';\nimport Tooltip from '@mui/material/Tooltip';\nimport CloseIcon from '@mui/icons-material/Close';\nimport Slide from '@mui/material/Slide';\nimport TextField from '@mui/material/TextField';\nimport SearchIcon from '@mui/icons-material/Search';\nimport NavigateNextIcon from '@mui/icons-material/NavigateNext';\nimport NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';\nimport CheckCircleIcon from '@mui/icons-material/CheckCircle';\n\n// Helper function to get correct image URL\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst getImageUrl = imagePath => {\n  if (!imagePath) {\n    return 'https://source.unsplash.com/800x400/?news';\n  }\n\n  // If it's already a full URL, return as is\n  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {\n    return imagePath;\n  }\n\n  // If it's new upload path (web-accessible)\n  if (imagePath.startsWith('/react-news/uploads/')) {\n    return `http://localhost${imagePath}`;\n  }\n\n  // If it's old upload path (legacy)\n  if (imagePath.startsWith('frontend/src/pages/admin/uploads/')) {\n    return `http://localhost/react-news/${imagePath}`;\n  }\n\n  // If it's relative upload path\n  if (imagePath.startsWith('uploads/')) {\n    return `http://localhost/react-news/${imagePath}`;\n  }\n\n  // If it's a React public path\n  if (imagePath.startsWith('/') && !imagePath.startsWith('/react-news/')) {\n    return `http://localhost:3000${imagePath}`;\n  }\n\n  // If it's already a react-news path\n  if (imagePath.startsWith('/react-news/')) {\n    return `http://localhost${imagePath}`;\n  }\n\n  // Default to uploads folder - sesuai dengan data berita yang diupload\n  return `http://localhost/react-news/uploads/${imagePath}`;\n};\n\n// Popular news will be fetched from database\n\nconst categories = ['Semua', 'Umum', 'Teknologi', 'Bisnis', 'Olahraga', 'Hiburan', 'Politik', 'Kesehatan'];\nfunction Toast({\n  message,\n  isVisible,\n  onClose\n}) {\n  if (!isVisible) return null;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'fixed',\n      top: {\n        xs: 80,\n        md: 100\n      },\n      right: {\n        xs: 16,\n        md: 24\n      },\n      zIndex: 9999,\n      bgcolor: '#10b981',\n      color: 'white',\n      px: 3,\n      py: 2,\n      borderRadius: 2,\n      boxShadow: 4,\n      display: 'flex',\n      alignItems: 'center',\n      gap: 1,\n      transform: 'translateX(0)',\n      transition: 'transform 0.3s ease-in-out',\n      maxWidth: {\n        xs: 'calc(100vw - 32px)',\n        md: 400\n      },\n      minWidth: {\n        xs: 280,\n        md: 320\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n      sx: {\n        fontSize: 20\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      sx: {\n        fontWeight: 600,\n        fontSize: {\n          xs: 13,\n          md: 14\n        }\n      },\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 5\n  }, this);\n}\n_c = Toast;\nfunction PopularTags({\n  selectedCategory,\n  setSelectedCategory,\n  categoriesData\n}) {\n  const popularTags = categoriesData.length > 0 ? categoriesData.map(cat => ({\n    name: cat.name,\n    count: cat.post_count || 0,\n    color: cat.color || '#3B82F6'\n  })) : [{\n    name: 'Umum',\n    count: 0,\n    color: '#6B7280'\n  }, {\n    name: 'Teknologi',\n    count: 0,\n    color: '#3B82F6'\n  }, {\n    name: 'Bisnis',\n    count: 0,\n    color: '#10B981'\n  }, {\n    name: 'Olahraga',\n    count: 0,\n    color: '#F59E0B'\n  }, {\n    name: 'Hiburan',\n    count: 0,\n    color: '#EF4444'\n  }, {\n    name: 'Politik',\n    count: 0,\n    color: '#8B5CF6'\n  }, {\n    name: 'Kesehatan',\n    count: 0,\n    color: '#06B6D4'\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      bgcolor: '#fff',\n      borderRadius: 3,\n      p: 3,\n      mb: 3,\n      boxShadow: 2,\n      border: '1px solid',\n      borderColor: 'grey.200'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 4,\n          height: 20,\n          bgcolor: 'primary.main',\n          borderRadius: 2,\n          mr: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          fontWeight: 700,\n          color: 'grey.800',\n          fontSize: {\n            xs: 16,\n            md: 18\n          }\n        },\n        children: \"Tag Terpopuler\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexWrap: 'wrap',\n        gap: 1.5,\n        justifyContent: {\n          xs: 'center',\n          md: 'flex-start'\n        }\n      },\n      children: popularTags.map((tag, index) => /*#__PURE__*/_jsxDEV(Chip, {\n        label: `${tag.name} (${tag.count})`,\n        onClick: () => setSelectedCategory(tag.name),\n        sx: {\n          bgcolor: selectedCategory === tag.name ? tag.color : 'grey.100',\n          color: selectedCategory === tag.name ? 'white' : 'grey.700',\n          fontWeight: 600,\n          fontSize: {\n            xs: 12,\n            md: 13\n          },\n          px: 2,\n          py: 1,\n          cursor: 'pointer',\n          transition: 'all 0.3s ease',\n          '&:hover': {\n            bgcolor: selectedCategory === tag.name ? tag.color : 'grey.200',\n            transform: 'translateY(-2px)',\n            boxShadow: 2\n          },\n          border: selectedCategory === tag.name ? 'none' : '1px solid',\n          borderColor: 'grey.300'\n        }\n      }, tag.name, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 129,\n    columnNumber: 5\n  }, this);\n}\n_c2 = PopularTags;\nfunction LatestPosts({\n  newsData\n}) {\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('id-ID', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Get latest 4 posts\n  const latestPosts = newsData.slice(0, 4);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      bgcolor: '#fff',\n      borderRadius: 3,\n      p: 3,\n      mb: 3,\n      boxShadow: 2,\n      border: '1px solid',\n      borderColor: 'grey.200'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 4,\n          height: 20,\n          bgcolor: 'primary.main',\n          borderRadius: 2,\n          mr: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          fontWeight: 700,\n          color: 'grey.800',\n          fontSize: {\n            xs: 16,\n            md: 18\n          }\n        },\n        children: \"Latest Posts\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Stack, {\n      spacing: 2,\n      children: latestPosts.map((post, index) => /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2,\n          p: 2,\n          borderRadius: 2,\n          transition: 'all 0.3s ease',\n          cursor: 'pointer',\n          '&:hover': {\n            bgcolor: 'grey.50',\n            transform: 'translateX(4px)'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flex: 1,\n            minWidth: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontWeight: 600,\n              color: 'grey.900',\n              lineHeight: 1.3,\n              mb: 0.5,\n              fontSize: {\n                xs: 13,\n                md: 14\n              },\n              overflow: 'hidden',\n              textOverflow: 'ellipsis',\n              display: '-webkit-box',\n              WebkitLineClamp: 2,\n              WebkitBoxOrient: 'vertical'\n            },\n            children: post.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1,\n              flexWrap: 'wrap'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: 'primary.main',\n                fontWeight: 600,\n                fontSize: {\n                  xs: 10,\n                  md: 11\n                }\n              },\n              children: post.category_name || post.category || 'Umum'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: 'grey.500',\n                fontSize: {\n                  xs: 10,\n                  md: 11\n                }\n              },\n              children: \"\\u2022\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: 'grey.600',\n                fontSize: {\n                  xs: 10,\n                  md: 11\n                }\n              },\n              children: formatTime(post.created_at || post.date)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: {\n              xs: 60,\n              md: 80\n            },\n            height: {\n              xs: 60,\n              md: 80\n            },\n            borderRadius: 2,\n            overflow: 'hidden',\n            flexShrink: 0\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            component: \"img\",\n            src: getImageUrl(post.image),\n            alt: post.title,\n            sx: {\n              width: '100%',\n              height: '100%',\n              objectFit: 'cover'\n            },\n            onError: e => {\n              e.target.src = 'https://source.unsplash.com/100x100/?news';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this)]\n      }, post.id || index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 200,\n    columnNumber: 5\n  }, this);\n}\n_c3 = LatestPosts;\nfunction BreakingNewsSlider({\n  newsData,\n  kostum\n}) {\n  _s();\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const [autoPlay, setAutoPlay] = useState(true);\n\n  // Add CSS animation for pulse effect\n  React.useEffect(() => {\n    const style = document.createElement('style');\n    style.textContent = `\n      @keyframes pulse {\n        0% { opacity: 1; }\n        50% { opacity: 0.5; }\n        100% { opacity: 1; }\n      }\n    `;\n    document.head.appendChild(style);\n    return () => document.head.removeChild(style);\n  }, []);\n\n  // Get latest 5 news for slider\n  const sliderNews = newsData.slice(0, 5);\n  React.useEffect(() => {\n    if (!autoPlay || sliderNews.length <= 1) return;\n    const interval = setInterval(() => {\n      setCurrentSlide(prev => (prev + 1) % sliderNews.length);\n    }, 5000); // Change slide every 5 seconds\n\n    return () => clearInterval(interval);\n  }, [autoPlay, sliderNews.length]);\n  const handleNext = () => {\n    setCurrentSlide(prev => (prev + 1) % sliderNews.length);\n    setAutoPlay(false);\n  };\n  const handlePrev = () => {\n    setCurrentSlide(prev => (prev - 1 + sliderNews.length) % sliderNews.length);\n    setAutoPlay(false);\n  };\n  const formatTime = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('id-ID', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  if (sliderNews.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        width: '100%',\n        height: {\n          xs: 200,\n          md: 400\n        },\n        borderRadius: 3,\n        overflow: 'hidden',\n        mb: 2,\n        boxShadow: 3,\n        bgcolor: 'grey.100',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: 'center',\n          color: 'grey.600'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 1,\n            fontWeight: 600\n          },\n          children: \"Belum ada berita\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: 'grey.500'\n          },\n          children: \"Berita akan muncul di sini\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'relative',\n      width: '100%',\n      height: {\n        xs: 200,\n        md: 400\n      },\n      // 1920x1080 aspect ratio for desktop\n      borderRadius: 3,\n      overflow: 'hidden',\n      mb: 2,\n      boxShadow: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 16,\n        left: 16,\n        zIndex: 3,\n        bgcolor: 'error.main',\n        color: 'white',\n        px: 2,\n        py: 0.5,\n        borderRadius: 2,\n        display: 'flex',\n        alignItems: 'center',\n        gap: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 8,\n          height: 8,\n          borderRadius: '50%',\n          bgcolor: 'white',\n          animation: 'pulse 1.5s infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        sx: {\n          fontWeight: 700,\n          fontSize: 12\n        },\n        children: \"BREAKING NEWS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        width: '100%',\n        height: '100%'\n      },\n      children: sliderNews.map((news, index) => /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          opacity: index === currentSlide ? 1 : 0,\n          transition: 'opacity 0.5s ease-in-out',\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'relative',\n            width: '100%',\n            height: '100%',\n            background: `linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.6)), url(${getImageUrl(news.image)})`,\n            backgroundSize: 'cover',\n            backgroundPosition: 'center',\n            display: 'flex',\n            alignItems: 'flex-end',\n            p: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              color: 'white',\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 700,\n                mb: 1,\n                textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                fontSize: {\n                  xs: 16,\n                  sm: 18,\n                  md: 24\n                }\n              },\n              children: news.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: 'grey.300',\n                textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                fontSize: {\n                  xs: 12,\n                  md: 14\n                }\n              },\n              children: [formatTime(news.date), \" \\u2022 \", news.category]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 13\n        }, this)\n      }, news.id || index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 7\n    }, this), sliderNews.length > 1 && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: handlePrev,\n        sx: {\n          position: 'absolute',\n          left: 8,\n          top: '50%',\n          transform: 'translateY(-50%)',\n          color: 'white',\n          '&:hover': {\n            bgcolor: 'rgba(0,0,0,0.1)'\n          },\n          zIndex: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(NavigateBeforeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: handleNext,\n        sx: {\n          position: 'absolute',\n          right: 8,\n          top: '50%',\n          transform: 'translateY(-50%)',\n          color: 'white',\n          '&:hover': {\n            bgcolor: 'rgba(0,0,0,0.1)'\n          },\n          zIndex: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(NavigateNextIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), sliderNews.length > 1 && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        bottom: 16,\n        left: '50%',\n        transform: 'translateX(-50%)',\n        display: 'flex',\n        gap: 1,\n        zIndex: 2\n      },\n      children: sliderNews.map((_, index) => /*#__PURE__*/_jsxDEV(Box, {\n        onClick: () => {\n          setCurrentSlide(index);\n          setAutoPlay(false);\n        },\n        sx: {\n          width: 8,\n          height: 8,\n          borderRadius: '50%',\n          bgcolor: index === currentSlide ? 'white' : 'rgba(255,255,255,0.5)',\n          cursor: 'pointer',\n          transition: 'all 0.3s ease'\n        }\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 378,\n    columnNumber: 5\n  }, this);\n}\n_s(BreakingNewsSlider, \"Y5JnQJuRbmpV4DtpHS9ZHFEhDk4=\");\n_c4 = BreakingNewsSlider;\nfunction NewsCard({\n  news,\n  expanded,\n  onExpand,\n  formatDate,\n  truncateText,\n  variant,\n  onShare,\n  onBookmark,\n  isBookmarked,\n  onNewsClick\n}) {\n  const aspectRatio = variant === 'desktop' ? '56.25%' : '100%';\n  const handleNewsClick = e => {\n    // Prevent navigation if clicking on buttons/icons\n    if (e.target.closest('button') || e.target.closest('[role=\"button\"]')) {\n      return;\n    }\n    onNewsClick && onNewsClick(news.id);\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100%',\n      position: 'relative',\n      minWidth: {\n        xs: '90vw',\n        sm: 320,\n        md: 340\n      },\n      maxWidth: {\n        xs: '100vw',\n        sm: 400,\n        md: 420\n      },\n      mx: 'auto',\n      borderRadius: 4,\n      boxShadow: 3,\n      p: 1,\n      bgcolor: '#fff',\n      transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',\n      '&:hover': {\n        transform: 'translateY(-4px)',\n        boxShadow: 6\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      onClick: handleNewsClick,\n      sx: {\n        position: 'relative',\n        width: '100%',\n        paddingTop: aspectRatio,\n        borderRadius: 3,\n        overflow: 'hidden',\n        cursor: 'pointer'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardMedia, {\n        component: \"img\",\n        sx: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          objectFit: 'cover'\n        },\n        image: getImageUrl(news.image),\n        alt: news.title,\n        onError: e => {\n          e.target.src = variant === 'desktop' ? 'https://source.unsplash.com/1920x1080/?news' : 'https://source.unsplash.com/900x900/?news';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 576,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        right: 20,\n        bottom: 20,\n        zIndex: 2,\n        mt: 2,\n        display: 'flex',\n        gap: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: isBookmarked ? \"Hapus dari bookmark\" : \"Tambah ke bookmark\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          color: isBookmarked ? \"success\" : \"primary\",\n          size: \"medium\",\n          onClick: e => {\n            e.stopPropagation(); // Prevent card click\n            onBookmark(news);\n          },\n          sx: {\n            transition: 'all 0.3s ease',\n            '&:hover': {\n              transform: 'scale(1.1)'\n            }\n          },\n          children: isBookmarked ? /*#__PURE__*/_jsxDEV(BookmarkAddedIcon, {\n            fontSize: \"medium\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(BookmarkAddIcon, {\n            fontSize: \"medium\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Bagikan\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          color: \"primary\",\n          size: \"medium\",\n          onClick: e => {\n            e.stopPropagation(); // Prevent card click\n            onShare(news);\n          },\n          children: /*#__PURE__*/_jsxDEV(ShareIcon, {\n            fontSize: \"medium\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 606,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n      onClick: handleNewsClick,\n      sx: {\n        flex: 1,\n        display: 'flex',\n        flexDirection: 'column',\n        pb: 2,\n        pt: 2,\n        cursor: 'pointer'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          fontWeight: 700,\n          color: 'primary.main',\n          mb: 0.5,\n          fontSize: {\n            xs: 18,\n            md: 22\n          }\n        },\n        children: news.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 631,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        sx: {\n          color: 'grey.600',\n          mb: 1,\n          fontSize: {\n            xs: 13,\n            md: 15\n          }\n        },\n        children: [\"Kategori: \", /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: 600,\n            color: '#2563eb'\n          },\n          children: news.category_name || news.category || 'Umum'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 21\n        }, this), \" \\u2022 \", formatDate(news.created_at || news.date)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 632,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          color: 'grey.800',\n          mb: 1,\n          flex: 1,\n          fontSize: {\n            xs: 15,\n            md: 16\n          }\n        },\n        children: expanded ? news.description : truncateText(news.description)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 635,\n        columnNumber: 9\n      }, this), news.description.length > 120 && /*#__PURE__*/_jsxDEV(Button, {\n        size: \"small\",\n        color: \"primary\",\n        onClick: e => {\n          e.stopPropagation(); // Prevent card click\n          onExpand();\n        },\n        sx: {\n          alignSelf: 'flex-start',\n          textTransform: 'none',\n          fontWeight: 600,\n          mt: 1\n        },\n        children: expanded ? 'Baca Lebih Sedikit' : 'Baca Selengkapnya'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 639,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 620,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 544,\n    columnNumber: 5\n  }, this);\n}\n_c5 = NewsCard;\nfunction MobileNewsLayout({\n  kostum,\n  newsData,\n  popularNews,\n  categoriesData,\n  selectedCategory,\n  setSelectedCategory,\n  expandedCards,\n  handleCardExpand,\n  loading,\n  bottomNav,\n  setBottomNav,\n  handleSidebar,\n  sidebarOpen,\n  handleSidebarClose,\n  handleShare,\n  onBookmark,\n  bookmarkedNews,\n  onSearchClick,\n  handleBottomNavChange,\n  onNewsClick\n}) {\n  const truncateText = (text, maxLength = 120) => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', {\n      day: 'numeric',\n      month: 'long',\n      year: 'numeric'\n    });\n  };\n  const filteredNews = newsData.filter(news => selectedCategory === 'Semua' || news.category === selectedCategory || news.category_name === selectedCategory).slice(0, 6);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      bgcolor: 'blue.50',\n      width: '100vw',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      color: \"inherit\",\n      elevation: 1,\n      sx: {\n        bgcolor: '#fff',\n        borderBottom: 1,\n        borderColor: 'grey.200',\n        zIndex: 1301\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        sx: {\n          minHeight: 70,\n          px: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            flexGrow: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: kostum.logo,\n            alt: \"Logo\",\n            sx: {\n              width: 48,\n              height: 48,\n              mr: 2\n            },\n            onError: e => {\n              e.target.src = '/logo192.png';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 676,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              fontSize: 22\n            },\n            children: kostum.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 675,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"end\",\n          color: \"primary\",\n          onClick: onSearchClick,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 684,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"end\",\n          color: \"primary\",\n          onClick: handleSidebar,\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 687,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 674,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 673,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      anchor: \"right\",\n      open: sidebarOpen,\n      onClose: handleSidebarClose,\n      ModalProps: {\n        keepMounted: true\n      },\n      sx: {\n        zIndex: 2000\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 260,\n          p: 3,\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleSidebarClose,\n          sx: {\n            position: 'absolute',\n            top: 8,\n            right: 8,\n            zIndex: 10\n          },\n          \"aria-label\": \"Tutup\",\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 701,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: kostum.logo,\n            alt: \"Logo\",\n            sx: {\n              width: 32,\n              height: 32,\n              mr: 1\n            },\n            onError: e => {\n              e.target.src = '/logo192.png';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              fontSize: 20\n            },\n            children: kostum.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 708,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 712,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            fullWidth: true,\n            sx: {\n              textTransform: 'none'\n            },\n            children: \"Favorite\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"primary\",\n            fullWidth: true,\n            sx: {\n              textTransform: 'none'\n            },\n            children: \"Lihat Berita\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 715,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 700,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 693,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        bgcolor: '#fff',\n        borderBottom: 1,\n        borderColor: 'grey.200',\n        py: 2,\n        position: 'fixed',\n        top: 60,\n        left: 0,\n        right: 0,\n        zIndex: 1301\n      },\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 2,\n        sx: {\n          overflowX: 'auto',\n          px: 2,\n          '&::-webkit-scrollbar': {\n            display: 'none'\n          },\n          scrollbarWidth: 'none',\n          msOverflowStyle: 'none'\n        },\n        children: categories.map(cat => /*#__PURE__*/_jsxDEV(Chip, {\n          label: cat,\n          color: selectedCategory === cat ? 'primary' : 'default',\n          onClick: () => setSelectedCategory(cat),\n          sx: {\n            fontWeight: 600,\n            cursor: 'pointer',\n            flexShrink: 0,\n            whiteSpace: 'nowrap'\n          }\n        }, cat, false, {\n          fileName: _jsxFileName,\n          lineNumber: 723,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 721,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 720,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 112,\n        left: 0,\n        right: 0,\n        bottom: 70,\n        px: 1,\n        maxWidth: '100%',\n        mx: 'auto',\n        width: '100%',\n        display: 'flex',\n        flexDirection: 'column',\n        minHeight: 0,\n        zIndex: 1,\n        pt: 2,\n        pb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        component: \"main\",\n        sx: {\n          flex: 1,\n          height: 'calc(100vh - 112px - 70px)',\n          // 112px = navbar+kategori, 70px = bottom nav\n          overflowY: 'auto',\n          minHeight: 0,\n          pt: 0,\n          pb: 0,\n          '&::-webkit-scrollbar': {\n            display: 'none'\n          },\n          scrollbarWidth: 'none',\n          msOverflowStyle: 'none'\n        },\n        children: [/*#__PURE__*/_jsxDEV(BreakingNewsSlider, {\n          newsData: newsData,\n          kostum: kostum\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PopularTags, {\n          selectedCategory: selectedCategory,\n          setSelectedCategory: setSelectedCategory,\n          categoriesData: categoriesData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 753,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LatestPosts, {\n          newsData: newsData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 756,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: 2,\n            overflowY: 'auto',\n            pb: {\n              xs: 10,\n              md: 0\n            },\n            // padding bawah ekstra di mobile\n            '&::-webkit-scrollbar': {\n              display: 'none'\n            },\n            scrollbarWidth: 'none',\n            msOverflowStyle: 'none',\n            px: 1,\n            pr: 2 // Add right padding to hide scrollbar\n          },\n          children: loading ? Array.from({\n            length: 6\n          }).map((_, idx) => /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              display: 'flex',\n              flexDirection: 'column',\n              height: '100%',\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: 'relative',\n                width: '100%',\n                paddingTop: '100%',\n                bgcolor: 'grey.200'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                flex: 1,\n                display: 'flex',\n                flexDirection: 'column',\n                pb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: 24,\n                  bgcolor: 'grey.200',\n                  mb: 1,\n                  borderRadius: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 777,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: 16,\n                  bgcolor: 'grey.100',\n                  mb: 1,\n                  borderRadius: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: 60,\n                  bgcolor: 'grey.100',\n                  borderRadius: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 779,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 776,\n              columnNumber: 19\n            }, this)]\n          }, idx, true, {\n            fileName: _jsxFileName,\n            lineNumber: 774,\n            columnNumber: 17\n          }, this)) : filteredNews.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              py: 8,\n              color: 'grey.500',\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Belum ada berita\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 785,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Berita akan muncul di sini\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 784,\n            columnNumber: 15\n          }, this) : filteredNews.map((news, idx) => /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: '100%'\n            },\n            children: /*#__PURE__*/_jsxDEV(NewsCard, {\n              news: news,\n              expanded: !!expandedCards[news.id],\n              onExpand: () => handleCardExpand(news.id),\n              formatDate: formatDate,\n              truncateText: truncateText,\n              variant: \"mobile\",\n              onShare: handleShare,\n              onBookmark: onBookmark,\n              isBookmarked: bookmarkedNews.has(news.id),\n              onNewsClick: onNewsClick\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 19\n            }, this)\n          }, news.id || idx, false, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 758,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 735,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 734,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'fixed',\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1300,\n        display: 'block',\n        backgroundColor: 'white',\n        borderTop: '1px solid #e0e0e0',\n        boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-around',\n          alignItems: 'center',\n          height: 64,\n          px: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => handleBottomNavChange(0),\n          className: `bottom-nav-item ${bottomNav === 0 ? 'active' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: `fas fa-home bottom-nav-icon ${bottomNav === 0 ? 'text-blue-600' : 'text-gray-500'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 832,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: bottomNav === 0 ? 'primary.main' : 'text.secondary'\n            },\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 828,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => handleBottomNavChange(1),\n          className: `bottom-nav-item ${bottomNav === 1 ? 'active' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: `fas fa-search bottom-nav-icon ${bottomNav === 1 ? 'text-blue-600' : 'text-gray-500'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 842,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: bottomNav === 1 ? 'primary.main' : 'text.secondary'\n            },\n            children: \"Cari\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 843,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 838,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          onClick: () => window.location.href = '/saved',\n          className: `bottom-nav-item ${bottomNav === 2 ? 'active' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: `fas fa-bookmark bottom-nav-icon ${bottomNav === 2 ? 'text-blue-600' : 'text-gray-500'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 852,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: \"bottom-nav-label\",\n            sx: {\n              color: bottomNav === 2 ? 'primary.main' : 'text.secondary'\n            },\n            children: \"Simpan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 848,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 821,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 810,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 671,\n    columnNumber: 5\n  }, this);\n}\n_c6 = MobileNewsLayout;\nfunction DesktopNewsLayout({\n  kostum,\n  newsData,\n  popularNews,\n  categoriesData,\n  selectedCategory,\n  setSelectedCategory,\n  expandedCards,\n  handleCardExpand,\n  loading,\n  handleShare,\n  onBookmark,\n  bookmarkedNews,\n  onSearchClick,\n  sidebarOpen,\n  handleSidebar,\n  handleSidebarClose,\n  onNewsClick\n}) {\n  const truncateText = (text, maxLength = 120) => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', {\n      day: 'numeric',\n      month: 'long',\n      year: 'numeric'\n    });\n  };\n  const filteredNews = newsData.filter(news => selectedCategory === 'Semua' || news.category === selectedCategory || news.category_name === selectedCategory).slice(0, 6);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      bgcolor: 'blue.50',\n      width: '100vw',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      color: \"inherit\",\n      elevation: 1,\n      sx: {\n        bgcolor: '#fff',\n        borderBottom: 1,\n        borderColor: 'grey.200',\n        zIndex: 1301\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        sx: {\n          minHeight: 80,\n          px: 6\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            flexGrow: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: kostum.logo,\n            alt: \"Logo\",\n            sx: {\n              width: 48,\n              height: 48,\n              mr: 2\n            },\n            onError: e => {\n              e.target.src = '/logo192.png';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 882,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              fontSize: 28\n            },\n            children: kostum.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 883,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 881,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"end\",\n          color: \"primary\",\n          onClick: onSearchClick,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 886,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 885,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          edge: \"end\",\n          color: \"primary\",\n          onClick: handleSidebar,\n          sx: {\n            ml: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {\n            fontSize: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 889,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 888,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 880,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 879,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      anchor: \"right\",\n      open: sidebarOpen,\n      onClose: handleSidebarClose,\n      ModalProps: {\n        keepMounted: true\n      },\n      sx: {\n        zIndex: 2000\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 260,\n          p: 3,\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleSidebarClose,\n          sx: {\n            position: 'absolute',\n            top: 8,\n            right: 8,\n            zIndex: 10\n          },\n          \"aria-label\": \"Tutup\",\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 906,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 901,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: kostum.logo,\n            alt: \"Logo\",\n            sx: {\n              width: 32,\n              height: 32,\n              mr: 1\n            },\n            onError: e => {\n              e.target.src = '/logo192.png';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 909,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              fontSize: 20\n            },\n            children: kostum.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 908,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 912,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            fullWidth: true,\n            sx: {\n              textTransform: 'none'\n            },\n            children: \"Favorite\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 914,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"primary\",\n            fullWidth: true,\n            sx: {\n              textTransform: 'none'\n            },\n            children: \"Lihat Berita\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 915,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 913,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 900,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 893,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        bgcolor: '#fff',\n        borderBottom: 1,\n        borderColor: 'grey.200',\n        py: 2,\n        position: 'fixed',\n        top: 70,\n        left: 0,\n        right: 0,\n        zIndex: 1301\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          maxWidth: '1200px',\n          mx: 'auto',\n          px: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          sx: {\n            overflowX: 'auto',\n            justifyContent: 'center',\n            '&::-webkit-scrollbar': {\n              display: 'none'\n            },\n            scrollbarWidth: 'none',\n            msOverflowStyle: 'none'\n          },\n          children: categories.map(cat => /*#__PURE__*/_jsxDEV(Chip, {\n            label: cat,\n            color: selectedCategory === cat ? 'primary' : 'default',\n            onClick: () => setSelectedCategory(cat),\n            sx: {\n              fontWeight: 600,\n              cursor: 'pointer',\n              flexShrink: 0,\n              whiteSpace: 'nowrap'\n            }\n          }, cat, false, {\n            fileName: _jsxFileName,\n            lineNumber: 929,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 921,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 920,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 919,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 128,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        px: 4,\n        maxWidth: '1200px',\n        mx: 'auto',\n        width: '100%',\n        display: 'flex',\n        flexDirection: 'row',\n        gap: 4,\n        minHeight: 0,\n        zIndex: 1,\n        pt: 3,\n        pb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        component: \"main\",\n        sx: {\n          flex: 1,\n          height: 'calc(100vh - 128px)',\n          // 128px = navbar+kategori desktop\n          overflowY: 'auto',\n          minHeight: 0,\n          pt: 0,\n          pb: 0,\n          '&::-webkit-scrollbar': {\n            display: 'none'\n          },\n          scrollbarWidth: 'none',\n          msOverflowStyle: 'none'\n        },\n        children: [/*#__PURE__*/_jsxDEV(BreakingNewsSlider, {\n          newsData: newsData,\n          kostum: kostum\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 956,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PopularTags, {\n          selectedCategory: selectedCategory,\n          setSelectedCategory: setSelectedCategory,\n          categoriesData: categoriesData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 959,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LatestPosts, {\n          newsData: newsData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 962,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexDirection: 'row',\n            gap: 4,\n            width: '100%',\n            maxWidth: '100%',\n            overflowX: 'auto',\n            '&::-webkit-scrollbar': {\n              display: 'none'\n            },\n            scrollbarWidth: 'none',\n            msOverflowStyle: 'none',\n            px: 1,\n            mt: 2,\n            pb: 2 // Add padding bottom to hide scrollbar\n          },\n          children: loading ? Array.from({\n            length: 6\n          }).map((_, idx) => /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              display: 'flex',\n              flexDirection: 'column',\n              height: '100%',\n              minWidth: 320,\n              flexShrink: 0\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: 'relative',\n                width: '100%',\n                paddingTop: '100%',\n                bgcolor: 'grey.200'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 981,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                flex: 1,\n                display: 'flex',\n                flexDirection: 'column',\n                pb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: 24,\n                  bgcolor: 'grey.200',\n                  mb: 1,\n                  borderRadius: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 983,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: 16,\n                  bgcolor: 'grey.100',\n                  mb: 1,\n                  borderRadius: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 984,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: 60,\n                  bgcolor: 'grey.100',\n                  borderRadius: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 985,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 982,\n              columnNumber: 19\n            }, this)]\n          }, idx, true, {\n            fileName: _jsxFileName,\n            lineNumber: 980,\n            columnNumber: 17\n          }, this)) : filteredNews.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              py: 8,\n              color: 'grey.500',\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Belum ada berita\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 991,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Berita akan muncul di sini\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 992,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 990,\n            columnNumber: 15\n          }, this) : filteredNews.map((news, idx) => /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              minWidth: 320,\n              flexShrink: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(NewsCard, {\n              news: news,\n              expanded: !!expandedCards[news.id],\n              onExpand: () => handleCardExpand(news.id),\n              formatDate: formatDate,\n              truncateText: truncateText,\n              variant: \"desktop\",\n              onShare: handleShare,\n              onBookmark: onBookmark,\n              isBookmarked: bookmarkedNews.has(news.id),\n              onNewsClick: onNewsClick\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 997,\n              columnNumber: 35\n            }, this)\n          }, news.id || idx, false, {\n            fileName: _jsxFileName,\n            lineNumber: 996,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 964,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 941,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        component: \"aside\",\n        sx: {\n          width: 320,\n          mt: 0,\n          display: 'block',\n          mb: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            bgcolor: '#fff',\n            borderRadius: 2,\n            boxShadow: 1,\n            p: 3,\n            mb: 6,\n            mt: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 700,\n              color: 'primary.main',\n              mb: 2\n            },\n            children: \"Berita Populer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1017,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1018,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 2,\n            children: popularNews.length > 0 ? popularNews.map((item, idx) => /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                src: getImageUrl(item.image),\n                alt: item.title,\n                sx: {\n                  width: 48,\n                  height: 48,\n                  mr: 1\n                },\n                onError: e => {\n                  e.target.src = 'https://source.unsplash.com/100x100/?news';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1023,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  sx: {\n                    fontWeight: 600,\n                    color: 'grey.900',\n                    lineHeight: 1.2\n                  },\n                  children: item.title.length > 50 ? item.title.substring(0, 50) + '...' : item.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1025,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    color: 'grey.600'\n                  },\n                  children: [formatDate(item.created_at || item.date), \" \\u2022 \", item.views || 0, \" views\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1028,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1024,\n                columnNumber: 21\n              }, this)]\n            }, item.id || idx, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1022,\n              columnNumber: 19\n            }, this)) : /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: 'grey.500',\n                textAlign: 'center'\n              },\n              children: \"Belum ada berita populer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1035,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1019,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1016,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1015,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 940,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 878,\n    columnNumber: 5\n  }, this);\n}\n_c7 = DesktopNewsLayout;\nexport default function LandingPage() {\n  _s2();\n  const navigate = useNavigate();\n  const [selectedCategory, setSelectedCategory] = useState('Semua');\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [bottomNav, setBottomNav] = useState(0);\n  const [kostum, setKostum] = useState({\n    logo: '',\n    title: ''\n  });\n  const [newsData, setNewsData] = useState([]);\n  const [popularNews, setPopularNews] = useState([]);\n  const [categoriesData, setCategoriesData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [expandedCards, setExpandedCards] = useState({});\n  const [searchModalOpen, setSearchModalOpen] = useState(false);\n  const [searchActive, setSearchActive] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [bookmarkedNews, setBookmarkedNews] = useState(new Set());\n  const [toast, setToast] = useState({\n    visible: false,\n    message: ''\n  });\n\n  // Check URL parameters on component mount\n  React.useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const kategori = urlParams.get('kategori');\n    const beritaId = urlParams.get('berita');\n    if (kategori && kategori !== 'Semua') {\n      setSelectedCategory(kategori);\n    }\n\n    // If there's a specific news ID, we could highlight it later\n    if (beritaId) {\n      // You can add logic here to highlight or scroll to the specific news\n      console.log('Shared news ID:', beritaId);\n    }\n  }, []);\n  const theme = useTheme();\n  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const handleSidebar = () => setSidebarOpen(true);\n  const handleSidebarClose = () => setSidebarOpen(false);\n  const handleShare = async news => {\n    try {\n      // Update share count in database\n      fetch('http://localhost:5000/api/posts/' + news.id + '/share', {\n        method: 'POST'\n      }).catch(err => console.error('Error updating share count:', err));\n\n      // Generate URL-friendly title for the link\n      const urlTitle = news.title.toLowerCase().replace(/[^a-z0-9\\s-]/g, '') // Remove special characters\n      .replace(/\\s+/g, '-') // Replace spaces with hyphens\n      .replace(/-+/g, '-') // Replace multiple hyphens with single\n      .trim();\n      const link = `${window.location.origin}/?berita=${news.id}&judul=${urlTitle}&kategori=${news.category}`;\n\n      // Copy to clipboard\n      await navigator.clipboard.writeText(link);\n\n      // Update local state to reflect share count increase\n      setNewsData(prevData => prevData.map(item => item.id === news.id ? {\n        ...item,\n        share: (item.share || 0) + 1\n      } : item));\n\n      // Show success toast\n      setToast({\n        visible: true,\n        message: 'Link berita berhasil disalin ke clipboard!'\n      });\n\n      // Auto-hide toast after 3 seconds\n      setTimeout(() => {\n        setToast({\n          visible: false,\n          message: ''\n        });\n      }, 3000);\n    } catch (error) {\n      console.error('Failed to copy link:', error);\n      setToast({\n        visible: true,\n        message: 'Gagal menyalin link berita'\n      });\n      setTimeout(() => {\n        setToast({\n          visible: false,\n          message: ''\n        });\n      }, 3000);\n    }\n  };\n  const handleBookmark = async news => {\n    const isCurrentlyBookmarked = bookmarkedNews.has(news.id);\n    try {\n      // Update bookmark status in database\n      const action = isCurrentlyBookmarked ? 'remove_saved_news' : 'add_saved_news';\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          action: action,\n          post_id: news.id\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const result = await response.json();\n      if (result.success) {\n        // Update local state\n        setBookmarkedNews(prev => {\n          const newSet = new Set(prev);\n          if (isCurrentlyBookmarked) {\n            newSet.delete(news.id);\n          } else {\n            newSet.add(news.id);\n          }\n          return newSet;\n        });\n\n        // Show toast notification\n        const message = isCurrentlyBookmarked ? 'Berita dihapus dari bookmark' : 'Berita ditambahkan ke bookmark';\n        setToast({\n          visible: true,\n          message\n        });\n        setTimeout(() => {\n          setToast({\n            visible: false,\n            message: ''\n          });\n        }, 3000);\n      } else {\n        setToast({\n          visible: true,\n          message: 'Gagal mengupdate bookmark: ' + (result.message || 'Unknown error')\n        });\n        setTimeout(() => {\n          setToast({\n            visible: false,\n            message: ''\n          });\n        }, 3000);\n      }\n    } catch (error) {\n      console.error('Error updating bookmark:', error);\n      setToast({\n        visible: true,\n        message: 'Error: ' + error.message\n      });\n      setTimeout(() => {\n        setToast({\n          visible: false,\n          message: ''\n        });\n      }, 3000);\n    }\n  };\n\n  // Handler untuk buka search overlay\n  const openSearch = () => {\n    setSearchActive(true);\n    setSearchQuery('');\n  };\n  // Handler untuk tutup search overlay\n  const closeSearch = () => {\n    setSearchActive(false);\n    setSearchQuery('');\n  };\n  // Filter berita sesuai query\n  const filteredSearch = searchQuery.trim() ? newsData.filter(n => n.title.toLowerCase().includes(searchQuery.toLowerCase()) || n.description.toLowerCase().includes(searchQuery.toLowerCase())).slice(0, 8) : [];\n  // Handler keyboard ESC\n  React.useEffect(() => {\n    if (!searchActive) return;\n    const handleKey = e => {\n      if (e.key === 'Escape') closeSearch();\n    };\n    window.addEventListener('keydown', handleKey);\n    return () => window.removeEventListener('keydown', handleKey);\n  }, [searchActive]);\n\n  // Load website settings from database\n  React.useEffect(() => {\n    // Get settings from database via PHP API\n    fetch('http://localhost/react-news/frontend/src/pages/admin/api/settings.php?action=get').then(res => res.json()).then(data => {\n      // Process logo path using getImageUrl function\n      let logoPath = getImageUrl(data.website_logo) || '/logo192.png';\n      setKostum({\n        logo: logoPath,\n        title: data.website_name || 'React News Portal',\n        description: data.website_description || 'Portal berita terkini dan terpercaya',\n        primary_color: data.primary_color || '#3B82F6',\n        secondary_color: data.secondary_color || '#10B981',\n        accent_color: data.accent_color || '#F59E0B',\n        footer_text: data.footer_text || '© 2024 React News Portal. All rights reserved.',\n        contact_email: data.contact_email || '<EMAIL>',\n        social_facebook: data.social_facebook || '',\n        social_twitter: data.social_twitter || '',\n        social_instagram: data.social_instagram || '',\n        social_youtube: data.social_youtube || '',\n        meta_keywords: data.meta_keywords || 'berita, news, portal',\n        meta_description: data.meta_description || 'Portal berita terkini'\n      });\n\n      // Update document title and meta tags\n      document.title = data.website_name || 'React News Portal';\n\n      // Update meta description\n      const metaDescription = document.querySelector('meta[name=\"description\"]');\n      if (metaDescription) {\n        metaDescription.setAttribute('content', data.meta_description || 'Portal berita terkini');\n      }\n\n      // Update meta keywords\n      const metaKeywords = document.querySelector('meta[name=\"keywords\"]');\n      if (metaKeywords) {\n        metaKeywords.setAttribute('content', data.meta_keywords || 'berita, news, portal');\n      }\n    }).catch(err => {\n      console.log('Database settings not available, using defaults:', err);\n      // Use default settings matching database.sql structure\n      setKostum({\n        logo: '/logo192.png',\n        title: 'React News Portal',\n        description: 'Portal berita terkini dan terpercaya',\n        primary_color: '#3B82F6',\n        secondary_color: '#10B981',\n        accent_color: '#F59E0B',\n        footer_text: '© 2024 React News Portal. All rights reserved.',\n        contact_email: '<EMAIL>',\n        social_facebook: '',\n        social_twitter: '',\n        social_instagram: '',\n        social_youtube: '',\n        meta_keywords: 'berita, news, portal, react',\n        meta_description: 'Portal berita terkini dengan teknologi React'\n      });\n\n      // Set default document title\n      document.title = 'React News Portal';\n    });\n  }, []);\n\n  // Function to refresh settings (can be called when admin updates settings)\n  const refreshSettings = React.useCallback(() => {\n    fetch('http://localhost/react-news/frontend/src/pages/admin/api/settings.php?action=get&t=' + Date.now()).then(res => res.json()).then(data => {\n      // Process logo path using getImageUrl function\n      let logoPath = getImageUrl(data.website_logo) || '/logo192.png';\n      setKostum(prev => ({\n        ...prev,\n        logo: logoPath,\n        title: data.website_name || 'React News Portal',\n        description: data.website_description || 'Portal berita terkini dan terpercaya'\n      }));\n      document.title = data.website_name || 'React News Portal';\n    }).catch(err => console.log('Failed to refresh settings:', err));\n  }, []);\n\n  // Listen for settings updates (optional - for real-time updates)\n  React.useEffect(() => {\n    const handleStorageChange = e => {\n      if (e.key === 'settings_updated') {\n        console.log('Settings updated, refreshing...');\n        refreshSettings();\n      }\n    };\n    window.addEventListener('storage', handleStorageChange);\n    return () => window.removeEventListener('storage', handleStorageChange);\n  }, [refreshSettings]);\n\n  // Load categories from database\n  React.useEffect(() => {\n    fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_categories').then(res => res.json()).then(data => {\n      if (data.success && Array.isArray(data.data)) {\n        // Map database structure to frontend format\n        const mappedCategories = data.data.map(cat => ({\n          id: cat.id,\n          name: cat.name,\n          slug: cat.slug,\n          description: cat.description,\n          color: cat.color || '#6B7280',\n          is_active: cat.is_active,\n          post_count: cat.post_count || 0,\n          created_at: cat.created_at,\n          updated_at: cat.updated_at\n        }));\n\n        // Filter only active categories\n        const activeCategories = mappedCategories.filter(cat => cat.is_active !== false);\n        setCategoriesData(activeCategories);\n      }\n    }).catch(err => {\n      console.log('Backend not running, using database structure categories');\n      // Fallback categories matching database.sql structure\n      setCategoriesData([{\n        id: 1,\n        name: 'Umum',\n        slug: 'umum',\n        description: 'Berita umum dan informasi terkini',\n        color: '#6B7280',\n        is_active: true,\n        post_count: 5\n      }, {\n        id: 2,\n        name: 'Teknologi',\n        slug: 'teknologi',\n        description: 'Berita teknologi dan inovasi digital',\n        color: '#3B82F6',\n        is_active: true,\n        post_count: 8\n      }, {\n        id: 3,\n        name: 'Bisnis',\n        slug: 'bisnis',\n        description: 'Berita bisnis dan ekonomi',\n        color: '#10B981',\n        is_active: true,\n        post_count: 3\n      }, {\n        id: 4,\n        name: 'Olahraga',\n        slug: 'olahraga',\n        description: 'Berita olahraga dan kompetisi',\n        color: '#F59E0B',\n        is_active: true,\n        post_count: 6\n      }, {\n        id: 5,\n        name: 'Hiburan',\n        slug: 'hiburan',\n        description: 'Berita hiburan dan selebriti',\n        color: '#EF4444',\n        is_active: true,\n        post_count: 4\n      }, {\n        id: 6,\n        name: 'Politik',\n        slug: 'politik',\n        description: 'Berita politik dan pemerintahan',\n        color: '#8B5CF6',\n        is_active: true,\n        post_count: 2\n      }, {\n        id: 7,\n        name: 'Kesehatan',\n        slug: 'kesehatan',\n        description: 'Berita kesehatan dan medis',\n        color: '#06B6D4',\n        is_active: true,\n        post_count: 7\n      }]);\n    });\n  }, []);\n\n  // Load news data from database\n  React.useEffect(() => {\n    fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_news').then(res => res.json()).then(data => {\n      if (data.success && Array.isArray(data.data)) {\n        // Map database structure to frontend format\n        const mappedData = data.data.map(item => {\n          var _item$content;\n          return {\n            id: item.id,\n            title: item.title,\n            slug: item.slug,\n            description: item.description || item.excerpt || ((_item$content = item.content) === null || _item$content === void 0 ? void 0 : _item$content.substring(0, 200)) + '...',\n            content: item.content,\n            image: item.image || 'https://source.unsplash.com/400x300/?news',\n            image_alt: item.image_alt || item.title,\n            category: item.category_name || 'Umum',\n            category_name: item.category_name || 'Umum',\n            category_id: item.category_id || 1,\n            category_color: item.category_color || '#6B7280',\n            status: item.status || 'published',\n            featured: item.featured || false,\n            tags: item.tags ? item.tags.split(',') : [],\n            views: item.views || 0,\n            share: item.share || 0,\n            likes: item.likes || 0,\n            comments_count: item.comments_count || 0,\n            reading_time: item.reading_time || 5,\n            date: item.date || item.created_at,\n            published_at: item.published_at,\n            created_at: item.created_at,\n            updated_at: item.updated_at,\n            author: item.author_name || item.full_name || 'Admin',\n            user_id: item.user_id\n          };\n        });\n\n        // Filter only published posts\n        const publishedPosts = mappedData.filter(item => item.status === 'published');\n        setNewsData(publishedPosts);\n\n        // Sort by engagement score for popular news\n        const popular = publishedPosts.sort((a, b) => {\n          const scoreA = a.views * 1 + a.share * 3 + a.likes * 2 + a.comments_count * 4;\n          const scoreB = b.views * 1 + b.share * 3 + b.likes * 2 + b.comments_count * 4;\n          return scoreB - scoreA;\n        }).slice(0, 5);\n        setPopularNews(popular);\n      } else {\n        setNewsData([]);\n        setPopularNews([]);\n      }\n      setLoading(false);\n    }).catch(() => {\n      console.log('Backend not running, using database structure dummy data');\n      // Fallback data matching database.sql structure\n      const dummyNews = [{\n        id: 1,\n        title: 'Selamat Datang di React News Portal',\n        slug: 'selamat-datang-di-react-news-portal',\n        description: 'Portal berita modern dengan teknologi React yang memberikan pengalaman membaca optimal untuk semua pengguna.',\n        content: 'Portal berita modern dengan teknologi React yang memberikan pengalaman membaca optimal untuk semua pengguna. Dengan desain responsif dan fitur-fitur canggih.',\n        image: 'https://source.unsplash.com/800x600/?technology,news',\n        image_alt: 'React News Portal',\n        category: 'Teknologi',\n        category_name: 'Teknologi',\n        category_id: 2,\n        category_color: '#3B82F6',\n        status: 'published',\n        featured: true,\n        tags: ['teknologi', 'react', 'portal', 'berita'],\n        views: 150,\n        share: 25,\n        likes: 45,\n        comments_count: 8,\n        reading_time: 3,\n        date: new Date().toISOString(),\n        published_at: new Date().toISOString(),\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n        author: 'Admin',\n        user_id: 1\n      }, {\n        id: 2,\n        title: 'Teknologi AI Terbaru Mengubah Dunia Digital',\n        slug: 'teknologi-ai-terbaru-mengubah-dunia-digital',\n        description: 'Perkembangan kecerdasan buatan yang revolusioner dalam berbagai industri dan kehidupan sehari-hari.',\n        content: 'Perkembangan kecerdasan buatan yang revolusioner dalam berbagai industri dan kehidupan sehari-hari. AI kini menjadi bagian integral dari transformasi digital.',\n        image: 'https://source.unsplash.com/800x600/?ai,technology',\n        image_alt: 'Teknologi AI',\n        category: 'Teknologi',\n        category_name: 'Teknologi',\n        category_id: 2,\n        category_color: '#3B82F6',\n        status: 'published',\n        featured: false,\n        tags: ['ai', 'teknologi', 'digital', 'inovasi'],\n        views: 89,\n        share: 12,\n        likes: 23,\n        comments_count: 5,\n        reading_time: 4,\n        date: new Date(Date.now() - 86400000).toISOString(),\n        published_at: new Date(Date.now() - 86400000).toISOString(),\n        created_at: new Date(Date.now() - 86400000).toISOString(),\n        updated_at: new Date(Date.now() - 86400000).toISOString(),\n        author: 'Admin',\n        user_id: 1\n      }, {\n        id: 3,\n        title: 'Tips Investasi untuk Pemula di Era Digital',\n        slug: 'tips-investasi-untuk-pemula-di-era-digital',\n        description: 'Panduan lengkap memulai investasi dengan platform digital yang aman dan terpercaya.',\n        content: 'Panduan lengkap memulai investasi dengan platform digital yang aman dan terpercaya. Pelajari strategi investasi yang tepat untuk pemula.',\n        image: 'https://source.unsplash.com/800x600/?investment,business',\n        image_alt: 'Investasi Digital',\n        category: 'Bisnis',\n        category_name: 'Bisnis',\n        category_id: 3,\n        category_color: '#10B981',\n        status: 'published',\n        featured: false,\n        tags: ['investasi', 'bisnis', 'digital', 'keuangan'],\n        views: 67,\n        share: 8,\n        likes: 15,\n        comments_count: 3,\n        reading_time: 6,\n        date: new Date(Date.now() - 172800000).toISOString(),\n        published_at: new Date(Date.now() - 172800000).toISOString(),\n        created_at: new Date(Date.now() - 172800000).toISOString(),\n        updated_at: new Date(Date.now() - 172800000).toISOString(),\n        author: 'Admin',\n        user_id: 1\n      }];\n      setNewsData(dummyNews);\n      setPopularNews(dummyNews);\n      setLoading(false);\n    });\n  }, []);\n\n  // Load bookmarked news from database\n  React.useEffect(() => {\n    const loadBookmarkedNews = async () => {\n      try {\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_saved_news');\n        const data = await response.json();\n        if (data.success && Array.isArray(data.data)) {\n          // Extract news IDs that are bookmarked\n          const bookmarkedIds = new Set(data.data.map(news => news.id));\n          setBookmarkedNews(bookmarkedIds);\n        }\n      } catch (error) {\n        console.log('Could not load bookmarked news:', error);\n      }\n    };\n    loadBookmarkedNews();\n  }, []);\n  const handleCardExpand = cardId => {\n    setExpandedCards(prev => ({\n      ...prev,\n      [cardId]: !prev[cardId]\n    }));\n  };\n  const handleNewsClick = async newsId => {\n    // Increment views when clicking news card\n    try {\n      await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_views&id=${newsId}`, {\n        method: 'POST'\n      });\n    } catch (error) {\n      console.log('Could not increment views:', error);\n    }\n\n    // Navigate to news detail\n    navigate(`/data-news/${newsId}`);\n  };\n\n  // Handler untuk klik search di bottom nav\n  const handleBottomNavChange = newValue => {\n    if (newValue === 0) {\n      setBottomNav(0);\n      // Already on home page, just update state\n    } else if (newValue === 1) {\n      setBottomNav(1);\n      openSearch(); // Search\n    } else if (newValue === 2) {\n      window.location.href = '/saved'; // Navigate to saved page\n    } else {\n      setBottomNav(newValue);\n    }\n  };\n  if (isDesktop) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(DesktopNewsLayout, {\n        kostum: kostum,\n        newsData: newsData,\n        popularNews: popularNews,\n        categoriesData: categoriesData,\n        selectedCategory: selectedCategory,\n        setSelectedCategory: setSelectedCategory,\n        expandedCards: expandedCards,\n        handleCardExpand: handleCardExpand,\n        loading: loading,\n        handleShare: handleShare,\n        onBookmark: handleBookmark,\n        bookmarkedNews: bookmarkedNews,\n        onSearchClick: openSearch,\n        sidebarOpen: sidebarOpen,\n        handleSidebar: handleSidebar,\n        handleSidebarClose: handleSidebarClose,\n        onNewsClick: handleNewsClick\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1616,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Toast, {\n        message: toast.message,\n        isVisible: toast.visible,\n        onClose: () => setToast({\n          visible: false,\n          message: ''\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1637,\n        columnNumber: 9\n      }, this), searchActive && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          zIndex: 2101,\n          bgcolor: 'rgba(255,255,255,0.98)',\n          boxShadow: 3,\n          px: 0,\n          pt: 0,\n          pb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            maxWidth: 500,\n            mx: 'auto',\n            position: 'relative',\n            mt: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            autoFocus: true,\n            fullWidth: true,\n            value: searchQuery,\n            onChange: e => setSearchQuery(e.target.value),\n            placeholder: \"Cari berita di sini...\",\n            variant: \"outlined\",\n            sx: {\n              fontSize: 18,\n              bgcolor: '#fff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1657,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: closeSearch,\n            sx: {\n              position: 'absolute',\n              top: 8,\n              right: 8,\n              zIndex: 10\n            },\n            \"aria-label\": \"Tutup\",\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1671,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1666,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1656,\n          columnNumber: 13\n        }, this), searchQuery.trim() && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            maxWidth: 500,\n            mx: 'auto',\n            mt: 1,\n            bgcolor: '#fff',\n            borderRadius: 2,\n            boxShadow: 1,\n            p: 1\n          },\n          children: filteredSearch.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              color: 'grey.500',\n              textAlign: 'center',\n              py: 2\n            },\n            children: \"Tidak ada berita ditemukan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1678,\n            columnNumber: 19\n          }, this) : filteredSearch.map(item => /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              py: 1,\n              px: 1,\n              borderBottom: '1px solid #eee',\n              cursor: 'pointer',\n              '&:last-child': {\n                borderBottom: 0\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 600,\n                color: 'primary.main'\n              },\n              children: item.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1684,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: 'grey.600'\n              },\n              children: [item.category, \" \\u2022 \", new Date(item.date).toLocaleDateString('id-ID', {\n                day: 'numeric',\n                month: 'long',\n                year: 'numeric'\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1685,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: 'grey.800'\n              },\n              children: item.description.length > 80 ? item.description.substring(0, 80) + '...' : item.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1686,\n              columnNumber: 23\n            }, this)]\n          }, item.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1683,\n            columnNumber: 21\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1676,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1644,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(MobileNewsLayout, {\n      kostum: kostum,\n      newsData: newsData,\n      popularNews: popularNews,\n      categoriesData: categoriesData,\n      selectedCategory: selectedCategory,\n      setSelectedCategory: setSelectedCategory,\n      expandedCards: expandedCards,\n      handleCardExpand: handleCardExpand,\n      loading: loading,\n      bottomNav: bottomNav,\n      setBottomNav: setBottomNav,\n      handleSidebar: handleSidebar,\n      sidebarOpen: sidebarOpen,\n      handleSidebarClose: handleSidebarClose,\n      handleShare: handleShare,\n      onBookmark: handleBookmark,\n      bookmarkedNews: bookmarkedNews,\n      onSearchClick: openSearch,\n      handleBottomNavChange: handleBottomNavChange,\n      onNewsClick: handleNewsClick\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1701,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Toast, {\n      message: toast.message,\n      isVisible: toast.visible,\n      onClose: () => setToast({\n        visible: false,\n        message: ''\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1725,\n      columnNumber: 7\n    }, this), searchActive && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        zIndex: 2101,\n        bgcolor: 'rgba(255,255,255,0.98)',\n        boxShadow: 3,\n        px: {\n          xs: 2,\n          md: 0\n        },\n        pt: {\n          xs: 2,\n          md: 0\n        },\n        // desktop: pt: 0 agar overlay tepat di atas\n        pb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          maxWidth: 500,\n          mx: 'auto',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          fullWidth: true,\n          value: searchQuery,\n          onChange: e => setSearchQuery(e.target.value),\n          placeholder: \"Cari berita di sini...\",\n          variant: \"outlined\",\n          sx: {\n            fontSize: 18,\n            bgcolor: '#fff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1745,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: closeSearch,\n          sx: {\n            position: 'absolute',\n            top: 8,\n            right: 8,\n            zIndex: 10\n          },\n          \"aria-label\": \"Tutup\",\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1759,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1754,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1744,\n        columnNumber: 11\n      }, this), searchQuery.trim() && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          maxWidth: 500,\n          mx: 'auto',\n          mt: 1,\n          bgcolor: '#fff',\n          borderRadius: 2,\n          boxShadow: 1,\n          p: 1\n        },\n        children: filteredSearch.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: 'grey.500',\n            textAlign: 'center',\n            py: 2\n          },\n          children: \"Tidak ada berita ditemukan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1766,\n          columnNumber: 17\n        }, this) : filteredSearch.map(item => /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            py: 1,\n            px: 1,\n            borderBottom: '1px solid #eee',\n            cursor: 'pointer',\n            '&:last-child': {\n              borderBottom: 0\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontWeight: 600,\n              color: 'primary.main'\n            },\n            children: item.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1772,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: 'grey.600'\n            },\n            children: [item.category, \" \\u2022 \", new Date(item.date).toLocaleDateString('id-ID', {\n              day: 'numeric',\n              month: 'long',\n              year: 'numeric'\n            })]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1773,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              color: 'grey.800'\n            },\n            children: item.description.length > 80 ? item.description.substring(0, 80) + '...' : item.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1774,\n            columnNumber: 21\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1771,\n          columnNumber: 19\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1764,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1732,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: searchModalOpen,\n      onClose: () => setSearchModalOpen(false),\n      fullScreen: isMobile,\n      TransitionComponent: Slide,\n      TransitionProps: {\n        direction: 'down'\n      },\n      sx: {\n        zIndex: 2100\n      },\n      PaperProps: {\n        sx: {\n          mt: 0,\n          borderRadius: {\n            xs: 0,\n            md: 3\n          },\n          width: {\n            xs: '100vw',\n            md: 500\n          },\n          mx: {\n            xs: 0,\n            md: 'auto'\n          },\n          top: 0,\n          position: 'absolute'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 3,\n          pt: 5,\n          position: 'relative',\n          bgcolor: '#fff',\n          minHeight: 120\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => setSearchModalOpen(false),\n          sx: {\n            position: 'absolute',\n            top: 12,\n            right: 12,\n            zIndex: 10\n          },\n          \"aria-label\": \"Tutup\",\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1808,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1803,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          fullWidth: true,\n          placeholder: \"Cari berita di sini...\",\n          variant: \"outlined\",\n          sx: {\n            fontSize: 18\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1810,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1802,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1784,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s2(LandingPage, \"tvMar0SjyxigOm1zau3x15aalDk=\", false, function () {\n  return [useNavigate, useTheme, useMediaQuery, useMediaQuery];\n});\n_c8 = LandingPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"Toast\");\n$RefreshReg$(_c2, \"PopularTags\");\n$RefreshReg$(_c3, \"LatestPosts\");\n$RefreshReg$(_c4, \"BreakingNewsSlider\");\n$RefreshReg$(_c5, \"NewsCard\");\n$RefreshReg$(_c6, \"MobileNewsLayout\");\n$RefreshReg$(_c7, \"DesktopNewsLayout\");\n$RefreshReg$(_c8, \"LandingPage\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Drawer", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "Box", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardMedia", "Avatar", "Divider", "MenuIcon", "Footer", "useMediaQuery", "useTheme", "ShareIcon", "BookmarkAddIcon", "BookmarkAddedIcon", "Dialog", "IconButton", "<PERSON><PERSON><PERSON>", "CloseIcon", "Slide", "TextField", "SearchIcon", "NavigateNextIcon", "NavigateBeforeIcon", "CheckCircleIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "getImageUrl", "imagePath", "startsWith", "categories", "Toast", "message", "isVisible", "onClose", "sx", "position", "top", "xs", "md", "right", "zIndex", "bgcolor", "color", "px", "py", "borderRadius", "boxShadow", "display", "alignItems", "gap", "transform", "transition", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "children", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "_c", "PopularTags", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "categoriesData", "popularTags", "length", "map", "cat", "name", "count", "post_count", "p", "mb", "border", "borderColor", "width", "height", "mr", "flexWrap", "justifyContent", "tag", "index", "label", "onClick", "cursor", "_c2", "LatestPosts", "newsData", "formatTime", "dateString", "date", "Date", "toLocaleTimeString", "hour", "minute", "latestPosts", "slice", "spacing", "post", "flex", "lineHeight", "overflow", "textOverflow", "WebkitLineClamp", "WebkitBoxOrient", "title", "category_name", "category", "created_at", "flexShrink", "component", "src", "image", "alt", "objectFit", "onError", "e", "target", "id", "_c3", "BreakingNewsSlider", "kostum", "_s", "currentSlide", "setCurrentSlide", "autoPlay", "setAutoPlay", "useEffect", "style", "document", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "sliderNews", "interval", "setInterval", "prev", "clearInterval", "handleNext", "handlePrev", "textAlign", "left", "animation", "news", "opacity", "background", "backgroundSize", "backgroundPosition", "textShadow", "sm", "bottom", "_", "_c4", "NewsCard", "expanded", "onExpand", "formatDate", "truncateText", "onShare", "onBookmark", "isBookmarked", "onNewsClick", "aspectRatio", "handleNewsClick", "closest", "flexDirection", "mx", "paddingTop", "mt", "size", "stopPropagation", "pb", "pt", "description", "alignSelf", "textTransform", "_c5", "MobileNewsLayout", "popularNews", "expandedCards", "handleCardExpand", "loading", "bottomNav", "setBottomNav", "handleSidebar", "sidebarOpen", "handleSidebarClose", "handleShare", "bookmarkedNews", "onSearchClick", "handleBottomNavChange", "text", "max<PERSON><PERSON><PERSON>", "substring", "toLocaleDateString", "day", "month", "year", "filteredNews", "filter", "minHeight", "elevation", "borderBottom", "flexGrow", "logo", "edge", "anchor", "open", "ModalProps", "keepMounted", "fullWidth", "direction", "overflowX", "scrollbarWidth", "msOverflowStyle", "whiteSpace", "overflowY", "pr", "Array", "from", "idx", "has", "backgroundColor", "borderTop", "className", "window", "location", "href", "_c6", "DesktopNewsLayout", "ml", "item", "views", "_c7", "LandingPage", "_s2", "navigate", "setSidebarOpen", "setKostum", "setNewsData", "setPopularNews", "setCategoriesData", "setLoading", "setExpandedCards", "searchModalOpen", "setSearchModalOpen", "searchActive", "setSearchActive", "searchQuery", "setSearch<PERSON>uery", "setBookmarkedNews", "Set", "toast", "setToast", "visible", "urlParams", "URLSearchParams", "search", "kate<PERSON>i", "get", "beritaId", "console", "log", "theme", "isDesktop", "breakpoints", "up", "isMobile", "down", "fetch", "method", "catch", "err", "error", "urlTitle", "toLowerCase", "replace", "trim", "link", "origin", "navigator", "clipboard", "writeText", "prevData", "share", "setTimeout", "handleBookmark", "isCurrentlyBookmarked", "action", "response", "headers", "body", "JSON", "stringify", "post_id", "ok", "Error", "status", "result", "json", "success", "newSet", "delete", "add", "openSearch", "closeSearch", "filteredSearch", "n", "includes", "handle<PERSON>ey", "key", "addEventListener", "removeEventListener", "then", "res", "data", "logoPath", "website_logo", "website_name", "website_description", "primary_color", "secondary_color", "accent_color", "footer_text", "contact_email", "social_facebook", "social_twitter", "social_instagram", "social_youtube", "meta_keywords", "meta_description", "metaDescription", "querySelector", "setAttribute", "metaKeywords", "refreshSettings", "useCallback", "now", "handleStorageChange", "isArray", "mappedCategories", "slug", "is_active", "updated_at", "activeCategories", "mappedData", "_item$content", "excerpt", "content", "image_alt", "category_id", "category_color", "featured", "tags", "split", "likes", "comments_count", "reading_time", "published_at", "author", "author_name", "full_name", "user_id", "publishedPosts", "popular", "sort", "a", "b", "scoreA", "scoreB", "dummyNews", "toISOString", "loadBookmarkedNews", "bookmarkedIds", "cardId", "newsId", "newValue", "autoFocus", "value", "onChange", "placeholder", "fullScreen", "TransitionComponent", "TransitionProps", "PaperProps", "_c8", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/pages/user/LandingPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport Drawer from '@mui/material/Drawer';\nimport AppBar from '@mui/material/AppBar';\nimport Toolbar from '@mui/material/Toolbar';\nimport Typography from '@mui/material/Typography';\nimport Box from '@mui/material/Box';\nimport Button from '@mui/material/Button';\nimport Chip from '@mui/material/Chip';\nimport Stack from '@mui/material/Stack';\nimport Card from '@mui/material/Card';\nimport CardContent from '@mui/material/CardContent';\nimport CardMedia from '@mui/material/CardMedia';\nimport Avatar from '@mui/material/Avatar';\nimport Divider from '@mui/material/Divider';\nimport MenuIcon from '@mui/icons-material/Menu';\nimport Footer from './components/Footer';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme } from '@mui/material/styles';\nimport ShareIcon from '@mui/icons-material/Share';\nimport BookmarkAddIcon from '@mui/icons-material/BookmarkAdd';\nimport BookmarkAddedIcon from '@mui/icons-material/BookmarkAdded';\nimport Dialog from '@mui/material/Dialog';\nimport IconButton from '@mui/material/IconButton';\nimport Tooltip from '@mui/material/Tooltip';\nimport CloseIcon from '@mui/icons-material/Close';\nimport Slide from '@mui/material/Slide';\nimport TextField from '@mui/material/TextField';\nimport SearchIcon from '@mui/icons-material/Search';\nimport NavigateNextIcon from '@mui/icons-material/NavigateNext';\nimport NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';\nimport CheckCircleIcon from '@mui/icons-material/CheckCircle';\n\n// Helper function to get correct image URL\nconst getImageUrl = (imagePath) => {\n  if (!imagePath) {\n    return 'https://source.unsplash.com/800x400/?news';\n  }\n\n  // If it's already a full URL, return as is\n  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {\n    return imagePath;\n  }\n\n  // If it's new upload path (web-accessible)\n  if (imagePath.startsWith('/react-news/uploads/')) {\n    return `http://localhost${imagePath}`;\n  }\n\n  // If it's old upload path (legacy)\n  if (imagePath.startsWith('frontend/src/pages/admin/uploads/')) {\n    return `http://localhost/react-news/${imagePath}`;\n  }\n\n  // If it's relative upload path\n  if (imagePath.startsWith('uploads/')) {\n    return `http://localhost/react-news/${imagePath}`;\n  }\n\n  // If it's a React public path\n  if (imagePath.startsWith('/') && !imagePath.startsWith('/react-news/')) {\n    return `http://localhost:3000${imagePath}`;\n  }\n\n  // If it's already a react-news path\n  if (imagePath.startsWith('/react-news/')) {\n    return `http://localhost${imagePath}`;\n  }\n\n  // Default to uploads folder - sesuai dengan data berita yang diupload\n  return `http://localhost/react-news/uploads/${imagePath}`;\n};\n\n// Popular news will be fetched from database\n\nconst categories = [\n  'Semua', 'Umum', 'Teknologi', 'Bisnis', 'Olahraga', 'Hiburan', 'Politik', 'Kesehatan'\n];\n\nfunction Toast({ message, isVisible, onClose }) {\n  if (!isVisible) return null;\n  \n  return (\n    <Box\n      sx={{\n        position: 'fixed',\n        top: { xs: 80, md: 100 },\n        right: { xs: 16, md: 24 },\n        zIndex: 9999,\n        bgcolor: '#10b981',\n        color: 'white',\n        px: 3,\n        py: 2,\n        borderRadius: 2,\n        boxShadow: 4,\n        display: 'flex',\n        alignItems: 'center',\n        gap: 1,\n        transform: 'translateX(0)',\n        transition: 'transform 0.3s ease-in-out',\n        maxWidth: { xs: 'calc(100vw - 32px)', md: 400 },\n        minWidth: { xs: 280, md: 320 }\n      }}\n    >\n      <CheckCircleIcon sx={{ fontSize: 20 }} />\n      <Typography variant=\"body2\" sx={{ fontWeight: 600, fontSize: { xs: 13, md: 14 } }}>\n        {message}\n      </Typography>\n    </Box>\n  );\n}\n\nfunction PopularTags({ selectedCategory, setSelectedCategory, categoriesData }) {\n  const popularTags = categoriesData.length > 0 ? categoriesData.map(cat => ({\n    name: cat.name,\n    count: cat.post_count || 0,\n    color: cat.color || '#3B82F6'\n  })) : [\n    { name: 'Umum', count: 0, color: '#6B7280' },\n    { name: 'Teknologi', count: 0, color: '#3B82F6' },\n    { name: 'Bisnis', count: 0, color: '#10B981' },\n    { name: 'Olahraga', count: 0, color: '#F59E0B' },\n    { name: 'Hiburan', count: 0, color: '#EF4444' },\n    { name: 'Politik', count: 0, color: '#8B5CF6' },\n    { name: 'Kesehatan', count: 0, color: '#06B6D4' }\n  ];\n\n  return (\n    <Box sx={{ \n      bgcolor: '#fff', \n      borderRadius: 3, \n      p: 3, \n      mb: 3, \n      boxShadow: 2,\n      border: '1px solid',\n      borderColor: 'grey.200'\n    }}>\n      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n        <Box sx={{ \n          width: 4, \n          height: 20, \n          bgcolor: 'primary.main', \n          borderRadius: 2, \n          mr: 2 \n        }} />\n        <Typography variant=\"h6\" sx={{ \n          fontWeight: 700, \n          color: 'grey.800',\n          fontSize: { xs: 16, md: 18 }\n        }}>\n          Tag Terpopuler\n        </Typography>\n      </Box>\n      \n      <Box sx={{ \n        display: 'flex', \n        flexWrap: 'wrap', \n        gap: 1.5,\n        justifyContent: { xs: 'center', md: 'flex-start' }\n      }}>\n        {popularTags.map((tag, index) => (\n          <Chip\n            key={tag.name}\n            label={`${tag.name} (${tag.count})`}\n            onClick={() => setSelectedCategory(tag.name)}\n            sx={{\n              bgcolor: selectedCategory === tag.name ? tag.color : 'grey.100',\n              color: selectedCategory === tag.name ? 'white' : 'grey.700',\n              fontWeight: 600,\n              fontSize: { xs: 12, md: 13 },\n              px: 2,\n              py: 1,\n              cursor: 'pointer',\n              transition: 'all 0.3s ease',\n              '&:hover': {\n                bgcolor: selectedCategory === tag.name ? tag.color : 'grey.200',\n                transform: 'translateY(-2px)',\n                boxShadow: 2\n              },\n              border: selectedCategory === tag.name ? 'none' : '1px solid',\n              borderColor: 'grey.300'\n            }}\n          />\n        ))}\n      </Box>\n    </Box>\n  );\n}\n\nfunction LatestPosts({ newsData }) {\n  const formatTime = (dateString) => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' });\n  };\n\n  // Get latest 4 posts\n  const latestPosts = newsData.slice(0, 4);\n\n  return (\n    <Box sx={{ \n      bgcolor: '#fff', \n      borderRadius: 3, \n      p: 3, \n      mb: 3, \n      boxShadow: 2,\n      border: '1px solid',\n      borderColor: 'grey.200'\n    }}>\n      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n        <Box sx={{ \n          width: 4, \n          height: 20, \n          bgcolor: 'primary.main', \n          borderRadius: 2, \n          mr: 2 \n        }} />\n        <Typography variant=\"h6\" sx={{ \n          fontWeight: 700, \n          color: 'grey.800',\n          fontSize: { xs: 16, md: 18 }\n        }}>\n          Latest Posts\n        </Typography>\n      </Box>\n      \n      <Stack spacing={2}>\n        {latestPosts.map((post, index) => (\n          <Box key={post.id || index} sx={{ \n            display: 'flex', \n            alignItems: 'center', \n            gap: 2,\n            p: 2,\n            borderRadius: 2,\n            transition: 'all 0.3s ease',\n            cursor: 'pointer',\n            '&:hover': {\n              bgcolor: 'grey.50',\n              transform: 'translateX(4px)'\n            }\n          }}>\n            {/* Text Content - Left Side */}\n            <Box sx={{ flex: 1, minWidth: 0 }}>\n              <Typography variant=\"subtitle2\" sx={{ \n                fontWeight: 600, \n                color: 'grey.900', \n                lineHeight: 1.3,\n                mb: 0.5,\n                fontSize: { xs: 13, md: 14 },\n                overflow: 'hidden',\n                textOverflow: 'ellipsis',\n                display: '-webkit-box',\n                WebkitLineClamp: 2,\n                WebkitBoxOrient: 'vertical'\n              }}>\n                {post.title}\n              </Typography>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>\n                <Typography variant=\"caption\" sx={{\n                  color: 'primary.main',\n                  fontWeight: 600,\n                  fontSize: { xs: 10, md: 11 }\n                }}>\n                  {post.category_name || post.category || 'Umum'}\n                </Typography>\n                <Typography variant=\"caption\" sx={{ color: 'grey.500', fontSize: { xs: 10, md: 11 } }}>\n                  •\n                </Typography>\n                <Typography variant=\"caption\" sx={{\n                  color: 'grey.600',\n                  fontSize: { xs: 10, md: 11 }\n                }}>\n                  {formatTime(post.created_at || post.date)}\n                </Typography>\n              </Box>\n            </Box>\n            \n            {/* Image - Right Side */}\n            <Box sx={{ \n              width: { xs: 60, md: 80 }, \n              height: { xs: 60, md: 80 }, \n              borderRadius: 2, \n              overflow: 'hidden',\n              flexShrink: 0\n            }}>\n              <Box\n                component=\"img\"\n                src={getImageUrl(post.image)}\n                alt={post.title}\n                sx={{\n                  width: '100%',\n                  height: '100%',\n                  objectFit: 'cover'\n                }}\n                onError={(e) => { e.target.src = 'https://source.unsplash.com/100x100/?news'; }}\n              />\n            </Box>\n          </Box>\n        ))}\n      </Stack>\n    </Box>\n  );\n}\n\nfunction BreakingNewsSlider({ newsData, kostum }) {\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const [autoPlay, setAutoPlay] = useState(true);\n\n  // Add CSS animation for pulse effect\n  React.useEffect(() => {\n    const style = document.createElement('style');\n    style.textContent = `\n      @keyframes pulse {\n        0% { opacity: 1; }\n        50% { opacity: 0.5; }\n        100% { opacity: 1; }\n      }\n    `;\n    document.head.appendChild(style);\n    return () => document.head.removeChild(style);\n  }, []);\n\n  // Get latest 5 news for slider\n  const sliderNews = newsData.slice(0, 5);\n\n  React.useEffect(() => {\n    if (!autoPlay || sliderNews.length <= 1) return;\n    \n    const interval = setInterval(() => {\n      setCurrentSlide((prev) => (prev + 1) % sliderNews.length);\n    }, 5000); // Change slide every 5 seconds\n\n    return () => clearInterval(interval);\n  }, [autoPlay, sliderNews.length]);\n\n  const handleNext = () => {\n    setCurrentSlide((prev) => (prev + 1) % sliderNews.length);\n    setAutoPlay(false);\n  };\n\n  const handlePrev = () => {\n    setCurrentSlide((prev) => (prev - 1 + sliderNews.length) % sliderNews.length);\n    setAutoPlay(false);\n  };\n\n  const formatTime = (dateString) => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' });\n  };\n\n  if (sliderNews.length === 0) {\n    return (\n      <Box sx={{ \n        position: 'relative', \n        width: '100%', \n        height: { xs: 200, md: 400 }, \n        borderRadius: 3, \n        overflow: 'hidden',\n        mb: 2,\n        boxShadow: 3,\n        bgcolor: 'grey.100',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      }}>\n        <Box sx={{ textAlign: 'center', color: 'grey.600' }}>\n          <Typography variant=\"h6\" sx={{ mb: 1, fontWeight: 600 }}>\n            Belum ada berita\n          </Typography>\n          <Typography variant=\"body2\" sx={{ color: 'grey.500' }}>\n            Berita akan muncul di sini\n          </Typography>\n        </Box>\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ \n      position: 'relative', \n      width: '100%', \n      height: { xs: 200, md: 400 }, // 1920x1080 aspect ratio for desktop\n      borderRadius: 3, \n      overflow: 'hidden',\n      mb: 2,\n      boxShadow: 3\n    }}>\n      {/* Breaking News Badge */}\n      <Box sx={{\n        position: 'absolute',\n        top: 16,\n        left: 16,\n        zIndex: 3,\n        bgcolor: 'error.main',\n        color: 'white',\n        px: 2,\n        py: 0.5,\n        borderRadius: 2,\n        display: 'flex',\n        alignItems: 'center',\n        gap: 1\n      }}>\n        <Box sx={{ \n          width: 8, \n          height: 8, \n          borderRadius: '50%', \n          bgcolor: 'white',\n          animation: 'pulse 1.5s infinite'\n        }} />\n        <Typography variant=\"caption\" sx={{ fontWeight: 700, fontSize: 12 }}>\n          BREAKING NEWS\n        </Typography>\n      </Box>\n\n      {/* Slides */}\n      <Box sx={{ position: 'relative', width: '100%', height: '100%' }}>\n        {sliderNews.map((news, index) => (\n          <Box\n            key={news.id || index}\n            sx={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              width: '100%',\n              height: '100%',\n              opacity: index === currentSlide ? 1 : 0,\n              transition: 'opacity 0.5s ease-in-out',\n              display: 'flex',\n              alignItems: 'center'\n            }}\n          >\n            <Box sx={{ \n              position: 'relative', \n              width: '100%', \n              height: '100%',\n              background: `linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.6)), url(${getImageUrl(news.image)})`,\n              backgroundSize: 'cover',\n              backgroundPosition: 'center',\n              display: 'flex',\n              alignItems: 'flex-end',\n              p: 3\n            }}>\n              <Box sx={{ color: 'white', width: '100%' }}>\n                <Typography variant=\"h6\" sx={{ \n                  fontWeight: 700, \n                  mb: 1,\n                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',\n                  fontSize: { xs: 16, sm: 18, md: 24 }\n                }}>\n                  {news.title}\n                </Typography>\n                <Typography variant=\"caption\" sx={{ \n                  color: 'grey.300',\n                  textShadow: '1px 1px 2px rgba(0,0,0,0.8)',\n                  fontSize: { xs: 12, md: 14 }\n                }}>\n                  {formatTime(news.date)} • {news.category}\n                </Typography>\n              </Box>\n            </Box>\n          </Box>\n        ))}\n      </Box>\n\n      {/* Navigation Arrows */}\n      {sliderNews.length > 1 && (\n        <>\n          <IconButton\n            onClick={handlePrev}\n            sx={{\n              position: 'absolute',\n              left: 8,\n              top: '50%',\n              transform: 'translateY(-50%)',\n              color: 'white',\n              '&:hover': { bgcolor: 'rgba(0,0,0,0.1)' },\n              zIndex: 2\n            }}\n          >\n            <NavigateBeforeIcon />\n          </IconButton>\n          <IconButton\n            onClick={handleNext}\n            sx={{\n              position: 'absolute',\n              right: 8,\n              top: '50%',\n              transform: 'translateY(-50%)',\n              color: 'white',\n              '&:hover': { bgcolor: 'rgba(0,0,0,0.1)' },\n              zIndex: 2\n            }}\n          >\n            <NavigateNextIcon />\n          </IconButton>\n        </>\n      )}\n\n      {/* Dots Indicator */}\n      {sliderNews.length > 1 && (\n        <Box sx={{\n          position: 'absolute',\n          bottom: 16,\n          left: '50%',\n          transform: 'translateX(-50%)',\n          display: 'flex',\n          gap: 1,\n          zIndex: 2\n        }}>\n          {sliderNews.map((_, index) => (\n            <Box\n              key={index}\n              onClick={() => {\n                setCurrentSlide(index);\n                setAutoPlay(false);\n              }}\n              sx={{\n                width: 8,\n                height: 8,\n                borderRadius: '50%',\n                bgcolor: index === currentSlide ? 'white' : 'rgba(255,255,255,0.5)',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease'\n              }}\n            />\n          ))}\n        </Box>\n      )}\n    </Box>\n  );\n}\n\nfunction NewsCard({ news, expanded, onExpand, formatDate, truncateText, variant, onShare, onBookmark, isBookmarked, onNewsClick }) {\n  const aspectRatio = variant === 'desktop' ? '56.25%' : '100%';\n\n  const handleNewsClick = (e) => {\n    // Prevent navigation if clicking on buttons/icons\n    if (e.target.closest('button') || e.target.closest('[role=\"button\"]')) {\n      return;\n    }\n    onNewsClick && onNewsClick(news.id);\n  };\n\n  return (\n    <Card\n      sx={{\n        display: 'flex',\n        flexDirection: 'column',\n        height: '100%',\n        position: 'relative',\n        minWidth: { xs: '90vw', sm: 320, md: 340 },\n        maxWidth: { xs: '100vw', sm: 400, md: 420 },\n        mx: 'auto',\n        borderRadius: 4,\n        boxShadow: 3,\n        p: 1,\n        bgcolor: '#fff',\n        transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',\n        '&:hover': {\n          transform: 'translateY(-4px)',\n          boxShadow: 6,\n        },\n      }}\n    >\n      {/* Clickable Image Area */}\n      <Box\n        onClick={handleNewsClick}\n        sx={{\n          position: 'relative',\n          width: '100%',\n          paddingTop: aspectRatio,\n          borderRadius: 3,\n          overflow: 'hidden',\n          cursor: 'pointer'\n        }}\n      >\n        <CardMedia\n          component=\"img\"\n          sx={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', objectFit: 'cover' }}\n          image={getImageUrl(news.image)}\n          alt={news.title}\n          onError={(e) => { e.target.src = variant === 'desktop' ? 'https://source.unsplash.com/1920x1080/?news' : 'https://source.unsplash.com/900x900/?news'; }}\n        />\n      </Box>\n      {/* Bookmark and Share icons kanan bawah */}\n      <Box sx={{ position: 'absolute', right: 20, bottom: 20, zIndex: 2, mt: 2, display: 'flex', gap: 1 }}>\n        <Tooltip title={isBookmarked ? \"Hapus dari bookmark\" : \"Tambah ke bookmark\"}>\n          <IconButton\n            color={isBookmarked ? \"success\" : \"primary\"}\n            size=\"medium\"\n            onClick={(e) => {\n              e.stopPropagation(); // Prevent card click\n              onBookmark(news);\n            }}\n            sx={{\n              transition: 'all 0.3s ease',\n              '&:hover': { transform: 'scale(1.1)' }\n            }}\n          >\n            {isBookmarked ? (\n              <BookmarkAddedIcon fontSize=\"medium\" />\n            ) : (\n              <BookmarkAddIcon fontSize=\"medium\" />\n            )}\n          </IconButton>\n        </Tooltip>\n        <Tooltip title=\"Bagikan\">\n          <IconButton\n            color=\"primary\"\n            size=\"medium\"\n            onClick={(e) => {\n              e.stopPropagation(); // Prevent card click\n              onShare(news);\n            }}\n          >\n            <ShareIcon fontSize=\"medium\" />\n          </IconButton>\n        </Tooltip>\n      </Box>\n      {/* Clickable Content Area */}\n      <CardContent\n        onClick={handleNewsClick}\n        sx={{\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          pb: 2,\n          pt: 2,\n          cursor: 'pointer'\n        }}\n      >\n        <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', mb: 0.5, fontSize: { xs: 18, md: 22 } }}>{news.title}</Typography>\n        <Typography variant=\"caption\" sx={{ color: 'grey.600', mb: 1, fontSize: { xs: 13, md: 15 } }}>\n          Kategori: <span style={{ fontWeight: 600, color: '#2563eb' }}>{news.category_name || news.category || 'Umum'}</span> &bull; {formatDate(news.created_at || news.date)}\n        </Typography>\n        <Typography variant=\"body2\" sx={{ color: 'grey.800', mb: 1, flex: 1, fontSize: { xs: 15, md: 16 } }}>\n          {expanded ? news.description : truncateText(news.description)}\n        </Typography>\n        {news.description.length > 120 && (\n          <Button\n            size=\"small\"\n            color=\"primary\"\n            onClick={(e) => {\n              e.stopPropagation(); // Prevent card click\n              onExpand();\n            }}\n            sx={{ alignSelf: 'flex-start', textTransform: 'none', fontWeight: 600, mt: 1 }}\n          >\n            {expanded ? 'Baca Lebih Sedikit' : 'Baca Selengkapnya'}\n          </Button>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n\nfunction MobileNewsLayout({ kostum, newsData, popularNews, categoriesData, selectedCategory, setSelectedCategory, expandedCards, handleCardExpand, loading, bottomNav, setBottomNav, handleSidebar, sidebarOpen, handleSidebarClose, handleShare, onBookmark, bookmarkedNews, onSearchClick, handleBottomNavChange, onNewsClick }) {\n  const truncateText = (text, maxLength = 120) => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' });\n  };\n  const filteredNews = newsData.filter(news =>\n    selectedCategory === 'Semua' ||\n    news.category === selectedCategory ||\n    news.category_name === selectedCategory\n  ).slice(0, 6);\n  return (\n    <Box sx={{ minHeight: '100vh', bgcolor: 'blue.50', width: '100vw', overflow: 'hidden' }}>\n      {/* Navbar (fixed) */}\n      <AppBar position=\"fixed\" color=\"inherit\" elevation={1} sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', zIndex: 1301 }}>\n        <Toolbar sx={{ minHeight: 70, px: 2 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\n            <Avatar \n              src={kostum.logo} \n              alt=\"Logo\" \n              sx={{ width: 48, height: 48, mr: 2 }}\n              onError={(e) => { e.target.src = '/logo192.png'; }}\n            />\n            <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', fontSize: 22 }}>{kostum.title}</Typography>\n          </Box>\n          <IconButton edge=\"end\" color=\"primary\" onClick={onSearchClick} sx={{ mr: 1 }}>\n            <SearchIcon fontSize=\"large\" />\n          </IconButton>\n          <IconButton edge=\"end\" color=\"primary\" onClick={handleSidebar}>\n            <MenuIcon fontSize=\"large\" />\n          </IconButton>\n        </Toolbar>\n      </AppBar>\n      {/* Sidebar Drawer (Mobile) */}\n      <Drawer\n        anchor=\"right\"\n        open={sidebarOpen}\n        onClose={handleSidebarClose}\n        ModalProps={{ keepMounted: true }}\n        sx={{ zIndex: 2000 }}\n      >\n        <Box sx={{ width: 260, p: 3, position: 'relative' }}>\n          <IconButton\n            onClick={handleSidebarClose}\n            sx={{ position: 'absolute', top: 8, right: 8, zIndex: 10 }}\n            aria-label=\"Tutup\"\n          >\n            <CloseIcon />\n          </IconButton>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n            <Avatar src={kostum.logo} alt=\"Logo\" sx={{ width: 32, height: 32, mr: 1 }} onError={(e) => { e.target.src = '/logo192.png'; }} />\n            <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', fontSize: 20 }}>{kostum.title}</Typography>\n          </Box>\n          <Divider sx={{ mb: 2 }} />\n          <Stack spacing={2}>\n            <Button variant=\"contained\" color=\"primary\" fullWidth sx={{ textTransform: 'none' }}>Favorite</Button>\n            <Button variant=\"outlined\" color=\"primary\" fullWidth sx={{ textTransform: 'none' }}>Lihat Berita</Button>\n          </Stack>\n        </Box>\n      </Drawer>\n      {/* Category Slider (fixed) */}\n      <Box sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', py: 2, position: 'fixed', top: 60, left: 0, right: 0, zIndex: 1301 }}>\n        <Stack direction=\"row\" spacing={2} sx={{ overflowX: 'auto', px: 2, '&::-webkit-scrollbar': { display: 'none' }, scrollbarWidth: 'none', msOverflowStyle: 'none' }}>\n          {categories.map((cat) => (\n            <Chip\n              key={cat}\n              label={cat}\n              color={selectedCategory === cat ? 'primary' : 'default'}\n              onClick={() => setSelectedCategory(cat)}\n              sx={{ fontWeight: 600, cursor: 'pointer', flexShrink: 0, whiteSpace: 'nowrap' }}\n            />\n          ))}\n        </Stack>\n      </Box>\n      {/* Main Content */}\n      <Box sx={{ position: 'absolute', top: 112, left: 0, right: 0, bottom: 70, px: 1, maxWidth: '100%', mx: 'auto', width: '100%', display: 'flex', flexDirection: 'column', minHeight: 0, zIndex: 1, pt: 2, pb: 2 }}>\n        <Box\n          component=\"main\"\n          sx={{\n            flex: 1,\n            height: 'calc(100vh - 112px - 70px)', // 112px = navbar+kategori, 70px = bottom nav\n            overflowY: 'auto',\n            minHeight: 0,\n            pt: 0,\n            pb: 0,\n            '&::-webkit-scrollbar': { display: 'none' },\n            scrollbarWidth: 'none',\n            msOverflowStyle: 'none',\n          }}\n        >\n          {/* Breaking News Slider */}\n          <BreakingNewsSlider newsData={newsData} kostum={kostum} />\n          \n          {/* Popular Tags */}\n          <PopularTags selectedCategory={selectedCategory} setSelectedCategory={setSelectedCategory} categoriesData={categoriesData} />\n\n          {/* Latest Posts */}\n          <LatestPosts newsData={newsData} />\n          \n          <Box\n            sx={{\n              display: 'flex',\n              flexDirection: 'column',\n              gap: 2,\n              overflowY: 'auto',\n              pb: { xs: 10, md: 0 }, // padding bawah ekstra di mobile\n              '&::-webkit-scrollbar': { display: 'none' },\n              scrollbarWidth: 'none',\n              msOverflowStyle: 'none',\n              px: 1,\n              pr: 2, // Add right padding to hide scrollbar\n            }}\n          >\n            {loading ? (\n              Array.from({ length: 6 }).map((_, idx) => (\n                <Card key={idx} sx={{ display: 'flex', flexDirection: 'column', height: '100%', width: '100%' }}>\n                  <Box sx={{ position: 'relative', width: '100%', paddingTop: '100%', bgcolor: 'grey.200' }} />\n                  <CardContent sx={{ flex: 1, display: 'flex', flexDirection: 'column', pb: 2 }}>\n                    <Box sx={{ height: 24, bgcolor: 'grey.200', mb: 1, borderRadius: 1 }} />\n                    <Box sx={{ height: 16, bgcolor: 'grey.100', mb: 1, borderRadius: 1 }} />\n                    <Box sx={{ height: 60, bgcolor: 'grey.100', borderRadius: 1 }} />\n                  </CardContent>\n                </Card>\n              ))\n            ) : filteredNews.length === 0 ? (\n              <Box sx={{ textAlign: 'center', py: 8, color: 'grey.500', width: '100%' }}>\n                <Typography variant=\"h6\">Belum ada berita</Typography>\n                <Typography variant=\"body2\">Berita akan muncul di sini</Typography>\n              </Box>\n            ) : (\n              filteredNews.map((news, idx) => (\n                <Box key={news.id || idx} sx={{ width: '100%' }}>\n                  <NewsCard\n                    news={news}\n                    expanded={!!expandedCards[news.id]}\n                    onExpand={() => handleCardExpand(news.id)}\n                    formatDate={formatDate}\n                    truncateText={truncateText}\n                    variant=\"mobile\"\n                    onShare={handleShare}\n                    onBookmark={onBookmark}\n                    isBookmarked={bookmarkedNews.has(news.id)}\n                    onNewsClick={onNewsClick}\n                  />\n                </Box>\n              ))\n            )}\n          </Box>\n        </Box>\n      </Box>\n      {/* Custom Bottom Navigation */}\n      <Box sx={{\n        position: 'fixed',\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1300,\n        display: 'block',\n        backgroundColor: 'white',\n        borderTop: '1px solid #e0e0e0',\n        boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'\n      }}>\n        <Box sx={{\n          display: 'flex',\n          justifyContent: 'space-around',\n          alignItems: 'center',\n          height: 64,\n          px: 1\n        }}>\n          <Box\n            onClick={() => handleBottomNavChange(0)}\n            className={`bottom-nav-item ${bottomNav === 0 ? 'active' : ''}`}\n          >\n            <i className={`fas fa-home bottom-nav-icon ${bottomNav === 0 ? 'text-blue-600' : 'text-gray-500'}`}></i>\n            <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: bottomNav === 0 ? 'primary.main' : 'text.secondary' }}>\n              Home\n            </Typography>\n          </Box>\n\n          <Box\n            onClick={() => handleBottomNavChange(1)}\n            className={`bottom-nav-item ${bottomNav === 1 ? 'active' : ''}`}\n          >\n            <i className={`fas fa-search bottom-nav-icon ${bottomNav === 1 ? 'text-blue-600' : 'text-gray-500'}`}></i>\n            <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: bottomNav === 1 ? 'primary.main' : 'text.secondary' }}>\n              Cari\n            </Typography>\n          </Box>\n\n          <Box\n            onClick={() => window.location.href = '/saved'}\n            className={`bottom-nav-item ${bottomNav === 2 ? 'active' : ''}`}\n          >\n            <i className={`fas fa-bookmark bottom-nav-icon ${bottomNav === 2 ? 'text-blue-600' : 'text-gray-500'}`}></i>\n            <Typography variant=\"caption\" className=\"bottom-nav-label\" sx={{ color: bottomNav === 2 ? 'primary.main' : 'text.secondary' }}>\n              Simpan\n            </Typography>\n          </Box>\n        </Box>\n      </Box>\n    </Box>\n  );\n}\n\nfunction DesktopNewsLayout({ kostum, newsData, popularNews, categoriesData, selectedCategory, setSelectedCategory, expandedCards, handleCardExpand, loading, handleShare, onBookmark, bookmarkedNews, onSearchClick, sidebarOpen, handleSidebar, handleSidebarClose, onNewsClick }) {\n  const truncateText = (text, maxLength = 120) => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' });\n  };\n  const filteredNews = newsData.filter(news =>\n    selectedCategory === 'Semua' ||\n    news.category === selectedCategory ||\n    news.category_name === selectedCategory\n  ).slice(0, 6);\n  return (\n    <Box sx={{ minHeight: '100vh', bgcolor: 'blue.50', width: '100vw', overflow: 'hidden' }}>\n      <AppBar position=\"fixed\" color=\"inherit\" elevation={1} sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', zIndex: 1301 }}>\n        <Toolbar sx={{ minHeight: 80, px: 6 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\n            <Avatar src={kostum.logo} alt=\"Logo\" sx={{ width: 48, height: 48, mr: 2 }} onError={(e) => { e.target.src = '/logo192.png'; }} />\n            <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', fontSize: 28 }}>{kostum.title}</Typography>\n          </Box>\n          <IconButton edge=\"end\" color=\"primary\" onClick={onSearchClick} sx={{ mr: 1 }}>\n            <SearchIcon fontSize=\"large\" />\n          </IconButton>\n          <IconButton edge=\"end\" color=\"primary\" onClick={handleSidebar} sx={{ ml: 1 }}>\n            <MenuIcon fontSize=\"large\" />\n          </IconButton>\n        </Toolbar>\n      </AppBar>\n      <Drawer\n        anchor=\"right\"\n        open={sidebarOpen}\n        onClose={handleSidebarClose}\n        ModalProps={{ keepMounted: true }}\n        sx={{ zIndex: 2000 }}\n      >\n        <Box sx={{ width: 260, p: 3, position: 'relative' }}>\n          <IconButton\n            onClick={handleSidebarClose}\n            sx={{ position: 'absolute', top: 8, right: 8, zIndex: 10 }}\n            aria-label=\"Tutup\"\n          >\n            <CloseIcon />\n          </IconButton>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n            <Avatar src={kostum.logo} alt=\"Logo\" sx={{ width: 32, height: 32, mr: 1 }} onError={(e) => { e.target.src = '/logo192.png'; }} />\n            <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', fontSize: 20 }}>{kostum.title}</Typography>\n          </Box>\n          <Divider sx={{ mb: 2 }} />\n          <Stack spacing={2}>\n            <Button variant=\"contained\" color=\"primary\" fullWidth sx={{ textTransform: 'none' }}>Favorite</Button>\n            <Button variant=\"outlined\" color=\"primary\" fullWidth sx={{ textTransform: 'none' }}>Lihat Berita</Button>\n          </Stack>\n        </Box>\n      </Drawer>\n      <Box sx={{ bgcolor: '#fff', borderBottom: 1, borderColor: 'grey.200', py: 2, position: 'fixed', top: 70, left: 0, right: 0, zIndex: 1301 }}>\n        <Box sx={{ maxWidth: '1200px', mx: 'auto', px: 4 }}>\n          <Stack direction=\"row\" spacing={2} sx={{ \n            overflowX: 'auto', \n            justifyContent: 'center',\n            '&::-webkit-scrollbar': { display: 'none' }, \n            scrollbarWidth: 'none', \n            msOverflowStyle: 'none' \n          }}>\n            {categories.map((cat) => (\n              <Chip\n                key={cat}\n                label={cat}\n                color={selectedCategory === cat ? 'primary' : 'default'}\n                onClick={() => setSelectedCategory(cat)}\n                sx={{ fontWeight: 600, cursor: 'pointer', flexShrink: 0, whiteSpace: 'nowrap' }}\n              />\n            ))}\n          </Stack>\n        </Box>\n      </Box>\n      <Box sx={{ position: 'absolute', top: 128, left: 0, right: 0, bottom: 0, px: 4, maxWidth: '1200px', mx: 'auto', width: '100%', display: 'flex', flexDirection: 'row', gap: 4, minHeight: 0, zIndex: 1, pt: 3, pb: 3 }}>\n        <Box\n          component=\"main\"\n          sx={{\n            flex: 1,\n            height: 'calc(100vh - 128px)', // 128px = navbar+kategori desktop\n            overflowY: 'auto',\n            minHeight: 0,\n            pt: 0,\n            pb: 0,\n            '&::-webkit-scrollbar': { display: 'none' },\n            scrollbarWidth: 'none',\n            msOverflowStyle: 'none',\n          }}\n        >\n          {/* Breaking News Slider */}\n          <BreakingNewsSlider newsData={newsData} kostum={kostum} />\n          \n          {/* Popular Tags */}\n          <PopularTags selectedCategory={selectedCategory} setSelectedCategory={setSelectedCategory} categoriesData={categoriesData} />\n\n          {/* Latest Posts */}\n          <LatestPosts newsData={newsData} />\n          \n          <Box sx={{ \n            display: 'flex',\n            flexDirection: 'row',\n            gap: 4,\n            width: '100%',\n            maxWidth: '100%',\n            overflowX: 'auto',\n            '&::-webkit-scrollbar': { display: 'none' },\n            scrollbarWidth: 'none',\n            msOverflowStyle: 'none',\n            px: 1,\n            mt: 2,\n            pb: 2, // Add padding bottom to hide scrollbar\n          }}>\n            {loading ? (\n              Array.from({ length: 6 }).map((_, idx) => (\n                <Card key={idx} sx={{ display: 'flex', flexDirection: 'column', height: '100%', minWidth: 320, flexShrink: 0 }}>\n                  <Box sx={{ position: 'relative', width: '100%', paddingTop: '100%', bgcolor: 'grey.200' }} />\n                  <CardContent sx={{ flex: 1, display: 'flex', flexDirection: 'column', pb: 2 }}>\n                    <Box sx={{ height: 24, bgcolor: 'grey.200', mb: 1, borderRadius: 1 }} />\n                    <Box sx={{ height: 16, bgcolor: 'grey.100', mb: 1, borderRadius: 1 }} />\n                    <Box sx={{ height: 60, bgcolor: 'grey.100', borderRadius: 1 }} />\n                  </CardContent>\n                </Card>\n              ))\n            ) : filteredNews.length === 0 ? (\n              <Box sx={{ textAlign: 'center', py: 8, color: 'grey.500', width: '100%' }}>\n                <Typography variant=\"h6\">Belum ada berita</Typography>\n                <Typography variant=\"body2\">Berita akan muncul di sini</Typography>\n              </Box>\n            ) : (\n              filteredNews.map((news, idx) => (\n                <Box key={news.id || idx} sx={{ minWidth: 320, flexShrink: 0 }}>\n                                  <NewsCard\n                  news={news}\n                  expanded={!!expandedCards[news.id]}\n                  onExpand={() => handleCardExpand(news.id)}\n                  formatDate={formatDate}\n                  truncateText={truncateText}\n                  variant=\"desktop\"\n                  onShare={handleShare}\n                  onBookmark={onBookmark}\n                  isBookmarked={bookmarkedNews.has(news.id)}\n                  onNewsClick={onNewsClick}\n                />\n                </Box>\n              ))\n            )}\n          </Box>\n        </Box>\n        {/* Aside: Popular News */}\n        <Box component=\"aside\" sx={{ width: 320, mt: 0, display: 'block', mb: 0 }}>\n          <Box sx={{ bgcolor: '#fff', borderRadius: 2, boxShadow: 1, p: 3, mb: 6, mt: 0 }}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 700, color: 'primary.main', mb: 2 }}>Berita Populer</Typography>\n            <Divider sx={{ mb: 2 }} />\n            <Stack spacing={2}>\n              {popularNews.length > 0 ? (\n                popularNews.map((item, idx) => (\n                  <Box key={item.id || idx} sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                    <Avatar src={getImageUrl(item.image)} alt={item.title} sx={{ width: 48, height: 48, mr: 1 }} onError={(e) => { e.target.src = 'https://source.unsplash.com/100x100/?news'; }} />\n                    <Box>\n                      <Typography variant=\"subtitle2\" sx={{ fontWeight: 600, color: 'grey.900', lineHeight: 1.2 }}>\n                        {item.title.length > 50 ? item.title.substring(0, 50) + '...' : item.title}\n                      </Typography>\n                      <Typography variant=\"caption\" sx={{ color: 'grey.600' }}>\n                        {formatDate(item.created_at || item.date)} • {item.views || 0} views\n                      </Typography>\n                    </Box>\n                  </Box>\n                ))\n              ) : (\n                <Typography variant=\"body2\" sx={{ color: 'grey.500', textAlign: 'center' }}>\n                  Belum ada berita populer\n                </Typography>\n              )}\n            </Stack>\n          </Box>\n        </Box>\n      </Box>\n    </Box>\n  );\n}\n\nexport default function LandingPage() {\n  const navigate = useNavigate();\n  const [selectedCategory, setSelectedCategory] = useState('Semua');\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [bottomNav, setBottomNav] = useState(0);\n  const [kostum, setKostum] = useState({ logo: '', title: '' });\n  const [newsData, setNewsData] = useState([]);\n  const [popularNews, setPopularNews] = useState([]);\n  const [categoriesData, setCategoriesData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [expandedCards, setExpandedCards] = useState({});\n  const [searchModalOpen, setSearchModalOpen] = useState(false);\n  const [searchActive, setSearchActive] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [bookmarkedNews, setBookmarkedNews] = useState(new Set());\n  const [toast, setToast] = useState({ visible: false, message: '' });\n\n  // Check URL parameters on component mount\n  React.useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const kategori = urlParams.get('kategori');\n    const beritaId = urlParams.get('berita');\n    \n    if (kategori && kategori !== 'Semua') {\n      setSelectedCategory(kategori);\n    }\n    \n    // If there's a specific news ID, we could highlight it later\n    if (beritaId) {\n      // You can add logic here to highlight or scroll to the specific news\n      console.log('Shared news ID:', beritaId);\n    }\n  }, []);\n  const theme = useTheme();\n  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n\n  const handleSidebar = () => setSidebarOpen(true);\n  const handleSidebarClose = () => setSidebarOpen(false);\n\n  const handleShare = async (news) => {\n    try {\n      // Update share count in database\n      fetch('http://localhost:5000/api/posts/' + news.id + '/share', {\n        method: 'POST'\n      }).catch(err => console.error('Error updating share count:', err));\n\n      // Generate URL-friendly title for the link\n      const urlTitle = news.title\n        .toLowerCase()\n        .replace(/[^a-z0-9\\s-]/g, '') // Remove special characters\n        .replace(/\\s+/g, '-') // Replace spaces with hyphens\n        .replace(/-+/g, '-') // Replace multiple hyphens with single\n        .trim();\n\n      const link = `${window.location.origin}/?berita=${news.id}&judul=${urlTitle}&kategori=${news.category}`;\n\n      // Copy to clipboard\n      await navigator.clipboard.writeText(link);\n\n      // Update local state to reflect share count increase\n      setNewsData(prevData =>\n        prevData.map(item =>\n          item.id === news.id\n            ? { ...item, share: (item.share || 0) + 1 }\n            : item\n        )\n      );\n\n      // Show success toast\n      setToast({ visible: true, message: 'Link berita berhasil disalin ke clipboard!' });\n\n      // Auto-hide toast after 3 seconds\n      setTimeout(() => {\n        setToast({ visible: false, message: '' });\n      }, 3000);\n\n    } catch (error) {\n      console.error('Failed to copy link:', error);\n      setToast({ visible: true, message: 'Gagal menyalin link berita' });\n      setTimeout(() => {\n        setToast({ visible: false, message: '' });\n      }, 3000);\n    }\n  };\n\n  const handleBookmark = async (news) => {\n    const isCurrentlyBookmarked = bookmarkedNews.has(news.id);\n\n    try {\n      // Update bookmark status in database\n      const action = isCurrentlyBookmarked ? 'remove_saved_news' : 'add_saved_news';\n\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          action: action,\n          post_id: news.id\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n\n      if (result.success) {\n        // Update local state\n        setBookmarkedNews(prev => {\n          const newSet = new Set(prev);\n          if (isCurrentlyBookmarked) {\n            newSet.delete(news.id);\n          } else {\n            newSet.add(news.id);\n          }\n          return newSet;\n        });\n\n        // Show toast notification\n        const message = isCurrentlyBookmarked\n          ? 'Berita dihapus dari bookmark'\n          : 'Berita ditambahkan ke bookmark';\n        setToast({ visible: true, message });\n\n        setTimeout(() => {\n          setToast({ visible: false, message: '' });\n        }, 3000);\n      } else {\n        setToast({ visible: true, message: 'Gagal mengupdate bookmark: ' + (result.message || 'Unknown error') });\n        setTimeout(() => {\n          setToast({ visible: false, message: '' });\n        }, 3000);\n      }\n    } catch (error) {\n      console.error('Error updating bookmark:', error);\n      setToast({ visible: true, message: 'Error: ' + error.message });\n      setTimeout(() => {\n        setToast({ visible: false, message: '' });\n      }, 3000);\n    }\n  };\n\n  // Handler untuk buka search overlay\n  const openSearch = () => {\n    setSearchActive(true);\n    setSearchQuery('');\n  };\n  // Handler untuk tutup search overlay\n  const closeSearch = () => {\n    setSearchActive(false);\n    setSearchQuery('');\n  };\n  // Filter berita sesuai query\n  const filteredSearch = searchQuery.trim() ? newsData.filter(\n    n =>\n      n.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n      n.description.toLowerCase().includes(searchQuery.toLowerCase())\n  ).slice(0, 8) : [];\n  // Handler keyboard ESC\n  React.useEffect(() => {\n    if (!searchActive) return;\n    const handleKey = (e) => {\n      if (e.key === 'Escape') closeSearch();\n    };\n    window.addEventListener('keydown', handleKey);\n    return () => window.removeEventListener('keydown', handleKey);\n  }, [searchActive]);\n\n  // Load website settings from database\n  React.useEffect(() => {\n    // Get settings from database via PHP API\n    fetch('http://localhost/react-news/frontend/src/pages/admin/api/settings.php?action=get')\n      .then(res => res.json())\n      .then(data => {\n        // Process logo path using getImageUrl function\n        let logoPath = getImageUrl(data.website_logo) || '/logo192.png';\n\n        setKostum({\n          logo: logoPath,\n          title: data.website_name || 'React News Portal',\n          description: data.website_description || 'Portal berita terkini dan terpercaya',\n          primary_color: data.primary_color || '#3B82F6',\n          secondary_color: data.secondary_color || '#10B981',\n          accent_color: data.accent_color || '#F59E0B',\n          footer_text: data.footer_text || '© 2024 React News Portal. All rights reserved.',\n          contact_email: data.contact_email || '<EMAIL>',\n          social_facebook: data.social_facebook || '',\n          social_twitter: data.social_twitter || '',\n          social_instagram: data.social_instagram || '',\n          social_youtube: data.social_youtube || '',\n          meta_keywords: data.meta_keywords || 'berita, news, portal',\n          meta_description: data.meta_description || 'Portal berita terkini'\n        });\n\n        // Update document title and meta tags\n        document.title = data.website_name || 'React News Portal';\n\n        // Update meta description\n        const metaDescription = document.querySelector('meta[name=\"description\"]');\n        if (metaDescription) {\n          metaDescription.setAttribute('content', data.meta_description || 'Portal berita terkini');\n        }\n\n        // Update meta keywords\n        const metaKeywords = document.querySelector('meta[name=\"keywords\"]');\n        if (metaKeywords) {\n          metaKeywords.setAttribute('content', data.meta_keywords || 'berita, news, portal');\n        }\n      })\n      .catch(err => {\n        console.log('Database settings not available, using defaults:', err);\n        // Use default settings matching database.sql structure\n        setKostum({\n          logo: '/logo192.png',\n          title: 'React News Portal',\n          description: 'Portal berita terkini dan terpercaya',\n          primary_color: '#3B82F6',\n          secondary_color: '#10B981',\n          accent_color: '#F59E0B',\n          footer_text: '© 2024 React News Portal. All rights reserved.',\n          contact_email: '<EMAIL>',\n          social_facebook: '',\n          social_twitter: '',\n          social_instagram: '',\n          social_youtube: '',\n          meta_keywords: 'berita, news, portal, react',\n          meta_description: 'Portal berita terkini dengan teknologi React'\n        });\n\n        // Set default document title\n        document.title = 'React News Portal';\n      });\n  }, []);\n\n  // Function to refresh settings (can be called when admin updates settings)\n  const refreshSettings = React.useCallback(() => {\n    fetch('http://localhost/react-news/frontend/src/pages/admin/api/settings.php?action=get&t=' + Date.now())\n      .then(res => res.json())\n      .then(data => {\n        // Process logo path using getImageUrl function\n        let logoPath = getImageUrl(data.website_logo) || '/logo192.png';\n\n        setKostum(prev => ({\n          ...prev,\n          logo: logoPath,\n          title: data.website_name || 'React News Portal',\n          description: data.website_description || 'Portal berita terkini dan terpercaya'\n        }));\n\n        document.title = data.website_name || 'React News Portal';\n      })\n      .catch(err => console.log('Failed to refresh settings:', err));\n  }, []);\n\n  // Listen for settings updates (optional - for real-time updates)\n  React.useEffect(() => {\n    const handleStorageChange = (e) => {\n      if (e.key === 'settings_updated') {\n        console.log('Settings updated, refreshing...');\n        refreshSettings();\n      }\n    };\n\n    window.addEventListener('storage', handleStorageChange);\n    return () => window.removeEventListener('storage', handleStorageChange);\n  }, [refreshSettings]);\n\n  // Load categories from database\n  React.useEffect(() => {\n    fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_categories')\n      .then(res => res.json())\n      .then(data => {\n        if (data.success && Array.isArray(data.data)) {\n          // Map database structure to frontend format\n          const mappedCategories = data.data.map(cat => ({\n            id: cat.id,\n            name: cat.name,\n            slug: cat.slug,\n            description: cat.description,\n            color: cat.color || '#6B7280',\n            is_active: cat.is_active,\n            post_count: cat.post_count || 0,\n            created_at: cat.created_at,\n            updated_at: cat.updated_at\n          }));\n\n          // Filter only active categories\n          const activeCategories = mappedCategories.filter(cat => cat.is_active !== false);\n          setCategoriesData(activeCategories);\n        }\n      })\n      .catch(err => {\n        console.log('Backend not running, using database structure categories');\n        // Fallback categories matching database.sql structure\n        setCategoriesData([\n          {\n            id: 1,\n            name: 'Umum',\n            slug: 'umum',\n            description: 'Berita umum dan informasi terkini',\n            color: '#6B7280',\n            is_active: true,\n            post_count: 5\n          },\n          {\n            id: 2,\n            name: 'Teknologi',\n            slug: 'teknologi',\n            description: 'Berita teknologi dan inovasi digital',\n            color: '#3B82F6',\n            is_active: true,\n            post_count: 8\n          },\n          {\n            id: 3,\n            name: 'Bisnis',\n            slug: 'bisnis',\n            description: 'Berita bisnis dan ekonomi',\n            color: '#10B981',\n            is_active: true,\n            post_count: 3\n          },\n          {\n            id: 4,\n            name: 'Olahraga',\n            slug: 'olahraga',\n            description: 'Berita olahraga dan kompetisi',\n            color: '#F59E0B',\n            is_active: true,\n            post_count: 6\n          },\n          {\n            id: 5,\n            name: 'Hiburan',\n            slug: 'hiburan',\n            description: 'Berita hiburan dan selebriti',\n            color: '#EF4444',\n            is_active: true,\n            post_count: 4\n          },\n          {\n            id: 6,\n            name: 'Politik',\n            slug: 'politik',\n            description: 'Berita politik dan pemerintahan',\n            color: '#8B5CF6',\n            is_active: true,\n            post_count: 2\n          },\n          {\n            id: 7,\n            name: 'Kesehatan',\n            slug: 'kesehatan',\n            description: 'Berita kesehatan dan medis',\n            color: '#06B6D4',\n            is_active: true,\n            post_count: 7\n          }\n        ]);\n      });\n  }, []);\n\n  // Load news data from database\n  React.useEffect(() => {\n    fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_news')\n      .then(res => res.json())\n      .then(data => {\n        if (data.success && Array.isArray(data.data)) {\n          // Map database structure to frontend format\n          const mappedData = data.data.map(item => ({\n            id: item.id,\n            title: item.title,\n            slug: item.slug,\n            description: item.description || item.excerpt || item.content?.substring(0, 200) + '...',\n            content: item.content,\n            image: item.image || 'https://source.unsplash.com/400x300/?news',\n            image_alt: item.image_alt || item.title,\n            category: item.category_name || 'Umum',\n            category_name: item.category_name || 'Umum',\n            category_id: item.category_id || 1,\n            category_color: item.category_color || '#6B7280',\n            status: item.status || 'published',\n            featured: item.featured || false,\n            tags: item.tags ? item.tags.split(',') : [],\n            views: item.views || 0,\n            share: item.share || 0,\n            likes: item.likes || 0,\n            comments_count: item.comments_count || 0,\n            reading_time: item.reading_time || 5,\n            date: item.date || item.created_at,\n            published_at: item.published_at,\n            created_at: item.created_at,\n            updated_at: item.updated_at,\n            author: item.author_name || item.full_name || 'Admin',\n            user_id: item.user_id\n          }));\n\n          // Filter only published posts\n          const publishedPosts = mappedData.filter(item => item.status === 'published');\n          setNewsData(publishedPosts);\n\n          // Sort by engagement score for popular news\n          const popular = publishedPosts\n            .sort((a, b) => {\n              const scoreA = (a.views * 1) + (a.share * 3) + (a.likes * 2) + (a.comments_count * 4);\n              const scoreB = (b.views * 1) + (b.share * 3) + (b.likes * 2) + (b.comments_count * 4);\n              return scoreB - scoreA;\n            })\n            .slice(0, 5);\n          setPopularNews(popular);\n        } else {\n          setNewsData([]);\n          setPopularNews([]);\n        }\n        setLoading(false);\n      })\n      .catch(() => {\n        console.log('Backend not running, using database structure dummy data');\n        // Fallback data matching database.sql structure\n        const dummyNews = [\n          {\n            id: 1,\n            title: 'Selamat Datang di React News Portal',\n            slug: 'selamat-datang-di-react-news-portal',\n            description: 'Portal berita modern dengan teknologi React yang memberikan pengalaman membaca optimal untuk semua pengguna.',\n            content: 'Portal berita modern dengan teknologi React yang memberikan pengalaman membaca optimal untuk semua pengguna. Dengan desain responsif dan fitur-fitur canggih.',\n            image: 'https://source.unsplash.com/800x600/?technology,news',\n            image_alt: 'React News Portal',\n            category: 'Teknologi',\n            category_name: 'Teknologi',\n            category_id: 2,\n            category_color: '#3B82F6',\n            status: 'published',\n            featured: true,\n            tags: ['teknologi', 'react', 'portal', 'berita'],\n            views: 150,\n            share: 25,\n            likes: 45,\n            comments_count: 8,\n            reading_time: 3,\n            date: new Date().toISOString(),\n            published_at: new Date().toISOString(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n            author: 'Admin',\n            user_id: 1\n          },\n          {\n            id: 2,\n            title: 'Teknologi AI Terbaru Mengubah Dunia Digital',\n            slug: 'teknologi-ai-terbaru-mengubah-dunia-digital',\n            description: 'Perkembangan kecerdasan buatan yang revolusioner dalam berbagai industri dan kehidupan sehari-hari.',\n            content: 'Perkembangan kecerdasan buatan yang revolusioner dalam berbagai industri dan kehidupan sehari-hari. AI kini menjadi bagian integral dari transformasi digital.',\n            image: 'https://source.unsplash.com/800x600/?ai,technology',\n            image_alt: 'Teknologi AI',\n            category: 'Teknologi',\n            category_name: 'Teknologi',\n            category_id: 2,\n            category_color: '#3B82F6',\n            status: 'published',\n            featured: false,\n            tags: ['ai', 'teknologi', 'digital', 'inovasi'],\n            views: 89,\n            share: 12,\n            likes: 23,\n            comments_count: 5,\n            reading_time: 4,\n            date: new Date(Date.now() - 86400000).toISOString(),\n            published_at: new Date(Date.now() - 86400000).toISOString(),\n            created_at: new Date(Date.now() - 86400000).toISOString(),\n            updated_at: new Date(Date.now() - 86400000).toISOString(),\n            author: 'Admin',\n            user_id: 1\n          },\n          {\n            id: 3,\n            title: 'Tips Investasi untuk Pemula di Era Digital',\n            slug: 'tips-investasi-untuk-pemula-di-era-digital',\n            description: 'Panduan lengkap memulai investasi dengan platform digital yang aman dan terpercaya.',\n            content: 'Panduan lengkap memulai investasi dengan platform digital yang aman dan terpercaya. Pelajari strategi investasi yang tepat untuk pemula.',\n            image: 'https://source.unsplash.com/800x600/?investment,business',\n            image_alt: 'Investasi Digital',\n            category: 'Bisnis',\n            category_name: 'Bisnis',\n            category_id: 3,\n            category_color: '#10B981',\n            status: 'published',\n            featured: false,\n            tags: ['investasi', 'bisnis', 'digital', 'keuangan'],\n            views: 67,\n            share: 8,\n            likes: 15,\n            comments_count: 3,\n            reading_time: 6,\n            date: new Date(Date.now() - 172800000).toISOString(),\n            published_at: new Date(Date.now() - 172800000).toISOString(),\n            created_at: new Date(Date.now() - 172800000).toISOString(),\n            updated_at: new Date(Date.now() - 172800000).toISOString(),\n            author: 'Admin',\n            user_id: 1\n          }\n        ];\n        setNewsData(dummyNews);\n        setPopularNews(dummyNews);\n        setLoading(false);\n      });\n  }, []);\n\n  // Load bookmarked news from database\n  React.useEffect(() => {\n    const loadBookmarkedNews = async () => {\n      try {\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_saved_news');\n        const data = await response.json();\n\n        if (data.success && Array.isArray(data.data)) {\n          // Extract news IDs that are bookmarked\n          const bookmarkedIds = new Set(data.data.map(news => news.id));\n          setBookmarkedNews(bookmarkedIds);\n        }\n      } catch (error) {\n        console.log('Could not load bookmarked news:', error);\n      }\n    };\n\n    loadBookmarkedNews();\n  }, []);\n\n  const handleCardExpand = (cardId) => {\n    setExpandedCards(prev => ({ ...prev, [cardId]: !prev[cardId] }));\n  };\n\n  const handleNewsClick = async (newsId) => {\n    // Increment views when clicking news card\n    try {\n      await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_views&id=${newsId}`, {\n        method: 'POST'\n      });\n    } catch (error) {\n      console.log('Could not increment views:', error);\n    }\n\n    // Navigate to news detail\n    navigate(`/data-news/${newsId}`);\n  };\n\n  // Handler untuk klik search di bottom nav\n  const handleBottomNavChange = (newValue) => {\n    if (newValue === 0) {\n      setBottomNav(0);\n      // Already on home page, just update state\n    } else if (newValue === 1) {\n      setBottomNav(1);\n      openSearch(); // Search\n    } else if (newValue === 2) {\n      window.location.href = '/saved'; // Navigate to saved page\n    } else {\n      setBottomNav(newValue);\n    }\n  };\n\n  if (isDesktop) {\n    return (\n      <>\n        <DesktopNewsLayout\n          kostum={kostum}\n          newsData={newsData}\n          popularNews={popularNews}\n          categoriesData={categoriesData}\n          selectedCategory={selectedCategory}\n          setSelectedCategory={setSelectedCategory}\n          expandedCards={expandedCards}\n          handleCardExpand={handleCardExpand}\n          loading={loading}\n          handleShare={handleShare}\n          onBookmark={handleBookmark}\n          bookmarkedNews={bookmarkedNews}\n          onSearchClick={openSearch}\n          sidebarOpen={sidebarOpen}\n          handleSidebar={handleSidebar}\n          handleSidebarClose={handleSidebarClose}\n          onNewsClick={handleNewsClick}\n        />\n        \n        {/* Toast Notification */}\n        <Toast \n          message={toast.message} \n          isVisible={toast.visible} \n          onClose={() => setToast({ visible: false, message: '' })} \n        />\n        {/* Search Overlay for Desktop */}\n        {searchActive && (\n          <Box sx={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            zIndex: 2101,\n            bgcolor: 'rgba(255,255,255,0.98)',\n            boxShadow: 3,\n            px: 0,\n            pt: 0,\n            pb: 2,\n          }}>\n            <Box sx={{ maxWidth: 500, mx: 'auto', position: 'relative', mt: 4 }}>\n              <TextField\n                autoFocus\n                fullWidth\n                value={searchQuery}\n                onChange={e => setSearchQuery(e.target.value)}\n                placeholder=\"Cari berita di sini...\"\n                variant=\"outlined\"\n                sx={{ fontSize: 18, bgcolor: '#fff' }}\n              />\n              <IconButton\n                onClick={closeSearch}\n                sx={{ position: 'absolute', top: 8, right: 8, zIndex: 10 }}\n                aria-label=\"Tutup\"\n              >\n                <CloseIcon />\n              </IconButton>\n            </Box>\n            {/* Hasil search */}\n            {searchQuery.trim() && (\n              <Box sx={{ maxWidth: 500, mx: 'auto', mt: 1, bgcolor: '#fff', borderRadius: 2, boxShadow: 1, p: 1 }}>\n                {filteredSearch.length === 0 ? (\n                  <Typography variant=\"body2\" sx={{ color: 'grey.500', textAlign: 'center', py: 2 }}>\n                    Tidak ada berita ditemukan\n                  </Typography>\n                ) : (\n                  filteredSearch.map((item) => (\n                    <Box key={item.id} sx={{ py: 1, px: 1, borderBottom: '1px solid #eee', cursor: 'pointer', '&:last-child': { borderBottom: 0 } }}>\n                      <Typography variant=\"subtitle2\" sx={{ fontWeight: 600, color: 'primary.main' }}>{item.title}</Typography>\n                      <Typography variant=\"caption\" sx={{ color: 'grey.600' }}>{item.category} &bull; {new Date(item.date).toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' })}</Typography>\n                      <Typography variant=\"body2\" sx={{ color: 'grey.800' }}>\n                        {item.description.length > 80 ? item.description.substring(0, 80) + '...' : item.description}\n                      </Typography>\n                    </Box>\n                  ))\n                )}\n              </Box>\n            )}\n          </Box>\n        )}\n      </>\n    );\n  }\n  return (\n    <>\n      <MobileNewsLayout\n        kostum={kostum}\n        newsData={newsData}\n        popularNews={popularNews}\n        categoriesData={categoriesData}\n        selectedCategory={selectedCategory}\n        setSelectedCategory={setSelectedCategory}\n        expandedCards={expandedCards}\n        handleCardExpand={handleCardExpand}\n        loading={loading}\n        bottomNav={bottomNav}\n        setBottomNav={setBottomNav}\n        handleSidebar={handleSidebar}\n        sidebarOpen={sidebarOpen}\n        handleSidebarClose={handleSidebarClose}\n        handleShare={handleShare}\n        onBookmark={handleBookmark}\n        bookmarkedNews={bookmarkedNews}\n        onSearchClick={openSearch}\n        handleBottomNavChange={handleBottomNavChange}\n        onNewsClick={handleNewsClick}\n      />\n      \n      {/* Toast Notification */}\n      <Toast \n        message={toast.message} \n        isVisible={toast.visible} \n        onClose={() => setToast({ visible: false, message: '' })} \n      />\n      {/* Overlay input search */}\n      {searchActive && (\n        <Box sx={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          zIndex: 2101,\n          bgcolor: 'rgba(255,255,255,0.98)',\n          boxShadow: 3,\n          px: { xs: 2, md: 0 },\n          pt: { xs: 2, md: 0 }, // desktop: pt: 0 agar overlay tepat di atas\n          pb: 2,\n        }}>\n          <Box sx={{ maxWidth: 500, mx: 'auto', position: 'relative' }}>\n            <TextField\n              autoFocus\n              fullWidth\n              value={searchQuery}\n              onChange={e => setSearchQuery(e.target.value)}\n              placeholder=\"Cari berita di sini...\"\n              variant=\"outlined\"\n              sx={{ fontSize: 18, bgcolor: '#fff' }}\n            />\n            <IconButton\n              onClick={closeSearch}\n              sx={{ position: 'absolute', top: 8, right: 8, zIndex: 10 }}\n              aria-label=\"Tutup\"\n            >\n              <CloseIcon />\n            </IconButton>\n          </Box>\n          {/* Hasil search */}\n          {searchQuery.trim() && (\n            <Box sx={{ maxWidth: 500, mx: 'auto', mt: 1, bgcolor: '#fff', borderRadius: 2, boxShadow: 1, p: 1 }}>\n              {filteredSearch.length === 0 ? (\n                <Typography variant=\"body2\" sx={{ color: 'grey.500', textAlign: 'center', py: 2 }}>\n                  Tidak ada berita ditemukan\n                </Typography>\n              ) : (\n                filteredSearch.map((item) => (\n                  <Box key={item.id} sx={{ py: 1, px: 1, borderBottom: '1px solid #eee', cursor: 'pointer', '&:last-child': { borderBottom: 0 } }}>\n                    <Typography variant=\"subtitle2\" sx={{ fontWeight: 600, color: 'primary.main' }}>{item.title}</Typography>\n                    <Typography variant=\"caption\" sx={{ color: 'grey.600' }}>{item.category} &bull; {new Date(item.date).toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' })}</Typography>\n                    <Typography variant=\"body2\" sx={{ color: 'grey.800' }}>\n                      {item.description.length > 80 ? item.description.substring(0, 80) + '...' : item.description}\n                    </Typography>\n                  </Box>\n                ))\n              )}\n            </Box>\n          )}\n        </Box>\n      )}\n      <Dialog\n        open={searchModalOpen}\n        onClose={() => setSearchModalOpen(false)}\n        fullScreen={isMobile}\n        TransitionComponent={Slide}\n        TransitionProps={{ direction: 'down' }}\n        sx={{ zIndex: 2100 }}\n        PaperProps={{\n          sx: {\n            mt: 0,\n            borderRadius: { xs: 0, md: 3 },\n            width: { xs: '100vw', md: 500 },\n            mx: { xs: 0, md: 'auto' },\n            top: 0,\n            position: 'absolute',\n          }\n        }}\n      >\n        <Box sx={{ p: 3, pt: 5, position: 'relative', bgcolor: '#fff', minHeight: 120 }}>\n          <IconButton\n            onClick={() => setSearchModalOpen(false)}\n            sx={{ position: 'absolute', top: 12, right: 12, zIndex: 10 }}\n            aria-label=\"Tutup\"\n          >\n            <CloseIcon />\n          </IconButton>\n          <TextField\n            autoFocus\n            fullWidth\n            placeholder=\"Cari berita di sini...\"\n            variant=\"outlined\"\n            sx={{ fontSize: 18 }}\n          />\n        </Box>\n      </Dialog>\n    </>\n  );\n}\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,eAAe,MAAM,iCAAiC;;AAE7D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,WAAW,GAAIC,SAAS,IAAK;EACjC,IAAI,CAACA,SAAS,EAAE;IACd,OAAO,2CAA2C;EACpD;;EAEA;EACA,IAAIA,SAAS,CAACC,UAAU,CAAC,SAAS,CAAC,IAAID,SAAS,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;IACvE,OAAOD,SAAS;EAClB;;EAEA;EACA,IAAIA,SAAS,CAACC,UAAU,CAAC,sBAAsB,CAAC,EAAE;IAChD,OAAO,mBAAmBD,SAAS,EAAE;EACvC;;EAEA;EACA,IAAIA,SAAS,CAACC,UAAU,CAAC,mCAAmC,CAAC,EAAE;IAC7D,OAAO,+BAA+BD,SAAS,EAAE;EACnD;;EAEA;EACA,IAAIA,SAAS,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;IACpC,OAAO,+BAA+BD,SAAS,EAAE;EACnD;;EAEA;EACA,IAAIA,SAAS,CAACC,UAAU,CAAC,GAAG,CAAC,IAAI,CAACD,SAAS,CAACC,UAAU,CAAC,cAAc,CAAC,EAAE;IACtE,OAAO,wBAAwBD,SAAS,EAAE;EAC5C;;EAEA;EACA,IAAIA,SAAS,CAACC,UAAU,CAAC,cAAc,CAAC,EAAE;IACxC,OAAO,mBAAmBD,SAAS,EAAE;EACvC;;EAEA;EACA,OAAO,uCAAuCA,SAAS,EAAE;AAC3D,CAAC;;AAED;;AAEA,MAAME,UAAU,GAAG,CACjB,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,CACtF;AAED,SAASC,KAAKA,CAAC;EAAEC,OAAO;EAAEC,SAAS;EAAEC;AAAQ,CAAC,EAAE;EAC9C,IAAI,CAACD,SAAS,EAAE,OAAO,IAAI;EAE3B,oBACET,OAAA,CAAC3B,GAAG;IACFsC,EAAE,EAAE;MACFC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE;QAAEC,EAAE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAI,CAAC;MACxBC,KAAK,EAAE;QAAEF,EAAE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAG,CAAC;MACzBE,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE,OAAO;MACdC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE,CAAC;MACLC,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,CAAC;MACZC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAE,CAAC;MACNC,SAAS,EAAE,eAAe;MAC1BC,UAAU,EAAE,4BAA4B;MACxCC,QAAQ,EAAE;QAAEf,EAAE,EAAE,oBAAoB;QAAEC,EAAE,EAAE;MAAI,CAAC;MAC/Ce,QAAQ,EAAE;QAAEhB,EAAE,EAAE,GAAG;QAAEC,EAAE,EAAE;MAAI;IAC/B,CAAE;IAAAgB,QAAA,gBAEF/B,OAAA,CAACF,eAAe;MAACa,EAAE,EAAE;QAAEqB,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzCpC,OAAA,CAAC5B,UAAU;MAACiE,OAAO,EAAC,OAAO;MAAC1B,EAAE,EAAE;QAAE2B,UAAU,EAAE,GAAG;QAAEN,QAAQ,EAAE;UAAElB,EAAE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAG;MAAE,CAAE;MAAAgB,QAAA,EAC/EvB;IAAO;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV;AAACG,EAAA,GA/BQhC,KAAK;AAiCd,SAASiC,WAAWA,CAAC;EAAEC,gBAAgB;EAAEC,mBAAmB;EAAEC;AAAe,CAAC,EAAE;EAC9E,MAAMC,WAAW,GAAGD,cAAc,CAACE,MAAM,GAAG,CAAC,GAAGF,cAAc,CAACG,GAAG,CAACC,GAAG,KAAK;IACzEC,IAAI,EAAED,GAAG,CAACC,IAAI;IACdC,KAAK,EAAEF,GAAG,CAACG,UAAU,IAAI,CAAC;IAC1B/B,KAAK,EAAE4B,GAAG,CAAC5B,KAAK,IAAI;EACtB,CAAC,CAAC,CAAC,GAAG,CACJ;IAAE6B,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE,CAAC;IAAE9B,KAAK,EAAE;EAAU,CAAC,EAC5C;IAAE6B,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE,CAAC;IAAE9B,KAAK,EAAE;EAAU,CAAC,EACjD;IAAE6B,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE,CAAC;IAAE9B,KAAK,EAAE;EAAU,CAAC,EAC9C;IAAE6B,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE,CAAC;IAAE9B,KAAK,EAAE;EAAU,CAAC,EAChD;IAAE6B,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE,CAAC;IAAE9B,KAAK,EAAE;EAAU,CAAC,EAC/C;IAAE6B,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE,CAAC;IAAE9B,KAAK,EAAE;EAAU,CAAC,EAC/C;IAAE6B,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE,CAAC;IAAE9B,KAAK,EAAE;EAAU,CAAC,CAClD;EAED,oBACEnB,OAAA,CAAC3B,GAAG;IAACsC,EAAE,EAAE;MACPO,OAAO,EAAE,MAAM;MACfI,YAAY,EAAE,CAAC;MACf6B,CAAC,EAAE,CAAC;MACJC,EAAE,EAAE,CAAC;MACL7B,SAAS,EAAE,CAAC;MACZ8B,MAAM,EAAE,WAAW;MACnBC,WAAW,EAAE;IACf,CAAE;IAAAvB,QAAA,gBACA/B,OAAA,CAAC3B,GAAG;MAACsC,EAAE,EAAE;QAAEa,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAE2B,EAAE,EAAE;MAAE,CAAE;MAAArB,QAAA,gBACxD/B,OAAA,CAAC3B,GAAG;QAACsC,EAAE,EAAE;UACP4C,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,EAAE;UACVtC,OAAO,EAAE,cAAc;UACvBI,YAAY,EAAE,CAAC;UACfmC,EAAE,EAAE;QACN;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACLpC,OAAA,CAAC5B,UAAU;QAACiE,OAAO,EAAC,IAAI;QAAC1B,EAAE,EAAE;UAC3B2B,UAAU,EAAE,GAAG;UACfnB,KAAK,EAAE,UAAU;UACjBa,QAAQ,EAAE;YAAElB,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG;QAC7B,CAAE;QAAAgB,QAAA,EAAC;MAEH;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAENpC,OAAA,CAAC3B,GAAG;MAACsC,EAAE,EAAE;QACPa,OAAO,EAAE,MAAM;QACfkC,QAAQ,EAAE,MAAM;QAChBhC,GAAG,EAAE,GAAG;QACRiC,cAAc,EAAE;UAAE7C,EAAE,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAa;MACnD,CAAE;MAAAgB,QAAA,EACCa,WAAW,CAACE,GAAG,CAAC,CAACc,GAAG,EAAEC,KAAK,kBAC1B7D,OAAA,CAACzB,IAAI;QAEHuF,KAAK,EAAE,GAAGF,GAAG,CAACZ,IAAI,KAAKY,GAAG,CAACX,KAAK,GAAI;QACpCc,OAAO,EAAEA,CAAA,KAAMrB,mBAAmB,CAACkB,GAAG,CAACZ,IAAI,CAAE;QAC7CrC,EAAE,EAAE;UACFO,OAAO,EAAEuB,gBAAgB,KAAKmB,GAAG,CAACZ,IAAI,GAAGY,GAAG,CAACzC,KAAK,GAAG,UAAU;UAC/DA,KAAK,EAAEsB,gBAAgB,KAAKmB,GAAG,CAACZ,IAAI,GAAG,OAAO,GAAG,UAAU;UAC3DV,UAAU,EAAE,GAAG;UACfN,QAAQ,EAAE;YAAElB,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG,CAAC;UAC5BK,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE,CAAC;UACL2C,MAAM,EAAE,SAAS;UACjBpC,UAAU,EAAE,eAAe;UAC3B,SAAS,EAAE;YACTV,OAAO,EAAEuB,gBAAgB,KAAKmB,GAAG,CAACZ,IAAI,GAAGY,GAAG,CAACzC,KAAK,GAAG,UAAU;YAC/DQ,SAAS,EAAE,kBAAkB;YAC7BJ,SAAS,EAAE;UACb,CAAC;UACD8B,MAAM,EAAEZ,gBAAgB,KAAKmB,GAAG,CAACZ,IAAI,GAAG,MAAM,GAAG,WAAW;UAC5DM,WAAW,EAAE;QACf;MAAE,GAnBGM,GAAG,CAACZ,IAAI;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBd,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC6B,GAAA,GA3EQzB,WAAW;AA6EpB,SAAS0B,WAAWA,CAAC;EAAEC;AAAS,CAAC,EAAE;EACjC,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;EACjF,CAAC;;EAED;EACA,MAAMC,WAAW,GAAGR,QAAQ,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAExC,oBACE5E,OAAA,CAAC3B,GAAG;IAACsC,EAAE,EAAE;MACPO,OAAO,EAAE,MAAM;MACfI,YAAY,EAAE,CAAC;MACf6B,CAAC,EAAE,CAAC;MACJC,EAAE,EAAE,CAAC;MACL7B,SAAS,EAAE,CAAC;MACZ8B,MAAM,EAAE,WAAW;MACnBC,WAAW,EAAE;IACf,CAAE;IAAAvB,QAAA,gBACA/B,OAAA,CAAC3B,GAAG;MAACsC,EAAE,EAAE;QAAEa,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAE2B,EAAE,EAAE;MAAE,CAAE;MAAArB,QAAA,gBACxD/B,OAAA,CAAC3B,GAAG;QAACsC,EAAE,EAAE;UACP4C,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,EAAE;UACVtC,OAAO,EAAE,cAAc;UACvBI,YAAY,EAAE,CAAC;UACfmC,EAAE,EAAE;QACN;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACLpC,OAAA,CAAC5B,UAAU;QAACiE,OAAO,EAAC,IAAI;QAAC1B,EAAE,EAAE;UAC3B2B,UAAU,EAAE,GAAG;UACfnB,KAAK,EAAE,UAAU;UACjBa,QAAQ,EAAE;YAAElB,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG;QAC7B,CAAE;QAAAgB,QAAA,EAAC;MAEH;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAENpC,OAAA,CAACxB,KAAK;MAACqG,OAAO,EAAE,CAAE;MAAA9C,QAAA,EACf4C,WAAW,CAAC7B,GAAG,CAAC,CAACgC,IAAI,EAAEjB,KAAK,kBAC3B7D,OAAA,CAAC3B,GAAG;QAAwBsC,EAAE,EAAE;UAC9Ba,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE,CAAC;UACNyB,CAAC,EAAE,CAAC;UACJ7B,YAAY,EAAE,CAAC;UACfM,UAAU,EAAE,eAAe;UAC3BoC,MAAM,EAAE,SAAS;UACjB,SAAS,EAAE;YACT9C,OAAO,EAAE,SAAS;YAClBS,SAAS,EAAE;UACb;QACF,CAAE;QAAAI,QAAA,gBAEA/B,OAAA,CAAC3B,GAAG;UAACsC,EAAE,EAAE;YAAEoE,IAAI,EAAE,CAAC;YAAEjD,QAAQ,EAAE;UAAE,CAAE;UAAAC,QAAA,gBAChC/B,OAAA,CAAC5B,UAAU;YAACiE,OAAO,EAAC,WAAW;YAAC1B,EAAE,EAAE;cAClC2B,UAAU,EAAE,GAAG;cACfnB,KAAK,EAAE,UAAU;cACjB6D,UAAU,EAAE,GAAG;cACf5B,EAAE,EAAE,GAAG;cACPpB,QAAQ,EAAE;gBAAElB,EAAE,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAG,CAAC;cAC5BkE,QAAQ,EAAE,QAAQ;cAClBC,YAAY,EAAE,UAAU;cACxB1D,OAAO,EAAE,aAAa;cACtB2D,eAAe,EAAE,CAAC;cAClBC,eAAe,EAAE;YACnB,CAAE;YAAArD,QAAA,EACC+C,IAAI,CAACO;UAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACbpC,OAAA,CAAC3B,GAAG;YAACsC,EAAE,EAAE;cAAEa,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE,CAAC;cAAEgC,QAAQ,EAAE;YAAO,CAAE;YAAA3B,QAAA,gBAC3E/B,OAAA,CAAC5B,UAAU;cAACiE,OAAO,EAAC,SAAS;cAAC1B,EAAE,EAAE;gBAChCQ,KAAK,EAAE,cAAc;gBACrBmB,UAAU,EAAE,GAAG;gBACfN,QAAQ,EAAE;kBAAElB,EAAE,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAG;cAC7B,CAAE;cAAAgB,QAAA,EACC+C,IAAI,CAACQ,aAAa,IAAIR,IAAI,CAACS,QAAQ,IAAI;YAAM;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACbpC,OAAA,CAAC5B,UAAU;cAACiE,OAAO,EAAC,SAAS;cAAC1B,EAAE,EAAE;gBAAEQ,KAAK,EAAE,UAAU;gBAAEa,QAAQ,EAAE;kBAAElB,EAAE,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAG;cAAE,CAAE;cAAAgB,QAAA,EAAC;YAEvF;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpC,OAAA,CAAC5B,UAAU;cAACiE,OAAO,EAAC,SAAS;cAAC1B,EAAE,EAAE;gBAChCQ,KAAK,EAAE,UAAU;gBACjBa,QAAQ,EAAE;kBAAElB,EAAE,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAG;cAC7B,CAAE;cAAAgB,QAAA,EACCqC,UAAU,CAACU,IAAI,CAACU,UAAU,IAAIV,IAAI,CAACR,IAAI;YAAC;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpC,OAAA,CAAC3B,GAAG;UAACsC,EAAE,EAAE;YACP4C,KAAK,EAAE;cAAEzC,EAAE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAG,CAAC;YACzByC,MAAM,EAAE;cAAE1C,EAAE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAG,CAAC;YAC1BO,YAAY,EAAE,CAAC;YACf2D,QAAQ,EAAE,QAAQ;YAClBQ,UAAU,EAAE;UACd,CAAE;UAAA1D,QAAA,eACA/B,OAAA,CAAC3B,GAAG;YACFqH,SAAS,EAAC,KAAK;YACfC,GAAG,EAAExF,WAAW,CAAC2E,IAAI,CAACc,KAAK,CAAE;YAC7BC,GAAG,EAAEf,IAAI,CAACO,KAAM;YAChB1E,EAAE,EAAE;cACF4C,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdsC,SAAS,EAAE;YACb,CAAE;YACFC,OAAO,EAAGC,CAAC,IAAK;cAAEA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,2CAA2C;YAAE;UAAE;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,GApEE0C,IAAI,CAACoB,EAAE,IAAIrC,KAAK;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqErB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV;AAAC+D,GAAA,GAhHQjC,WAAW;AAkHpB,SAASkC,kBAAkBA,CAAC;EAAEjC,QAAQ;EAAEkC;AAAO,CAAC,EAAE;EAAAC,EAAA;EAChD,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGzI,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC0I,QAAQ,EAAEC,WAAW,CAAC,GAAG3I,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACAD,KAAK,CAAC6I,SAAS,CAAC,MAAM;IACpB,MAAMC,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CF,KAAK,CAACG,WAAW,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA,KAAK;IACDF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC;IAChC,OAAO,MAAMC,QAAQ,CAACG,IAAI,CAACE,WAAW,CAACN,KAAK,CAAC;EAC/C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,UAAU,GAAGhD,QAAQ,CAACS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAEvC9G,KAAK,CAAC6I,SAAS,CAAC,MAAM;IACpB,IAAI,CAACF,QAAQ,IAAIU,UAAU,CAACtE,MAAM,IAAI,CAAC,EAAE;IAEzC,MAAMuE,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCb,eAAe,CAAEc,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIH,UAAU,CAACtE,MAAM,CAAC;IAC3D,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAM0E,aAAa,CAACH,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACX,QAAQ,EAAEU,UAAU,CAACtE,MAAM,CAAC,CAAC;EAEjC,MAAM2E,UAAU,GAAGA,CAAA,KAAM;IACvBhB,eAAe,CAAEc,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIH,UAAU,CAACtE,MAAM,CAAC;IACzD6D,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMe,UAAU,GAAGA,CAAA,KAAM;IACvBjB,eAAe,CAAEc,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,GAAGH,UAAU,CAACtE,MAAM,IAAIsE,UAAU,CAACtE,MAAM,CAAC;IAC7E6D,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMtC,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;EACjF,CAAC;EAED,IAAIyC,UAAU,CAACtE,MAAM,KAAK,CAAC,EAAE;IAC3B,oBACE7C,OAAA,CAAC3B,GAAG;MAACsC,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpB2C,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE;UAAE1C,EAAE,EAAE,GAAG;UAAEC,EAAE,EAAE;QAAI,CAAC;QAC5BO,YAAY,EAAE,CAAC;QACf2D,QAAQ,EAAE,QAAQ;QAClB7B,EAAE,EAAE,CAAC;QACL7B,SAAS,EAAE,CAAC;QACZL,OAAO,EAAE,UAAU;QACnBM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBkC,cAAc,EAAE;MAClB,CAAE;MAAA5B,QAAA,eACA/B,OAAA,CAAC3B,GAAG;QAACsC,EAAE,EAAE;UAAE+G,SAAS,EAAE,QAAQ;UAAEvG,KAAK,EAAE;QAAW,CAAE;QAAAY,QAAA,gBAClD/B,OAAA,CAAC5B,UAAU;UAACiE,OAAO,EAAC,IAAI;UAAC1B,EAAE,EAAE;YAAEyC,EAAE,EAAE,CAAC;YAAEd,UAAU,EAAE;UAAI,CAAE;UAAAP,QAAA,EAAC;QAEzD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpC,OAAA,CAAC5B,UAAU;UAACiE,OAAO,EAAC,OAAO;UAAC1B,EAAE,EAAE;YAAEQ,KAAK,EAAE;UAAW,CAAE;UAAAY,QAAA,EAAC;QAEvD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEpC,OAAA,CAAC3B,GAAG;IAACsC,EAAE,EAAE;MACPC,QAAQ,EAAE,UAAU;MACpB2C,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE;QAAE1C,EAAE,EAAE,GAAG;QAAEC,EAAE,EAAE;MAAI,CAAC;MAAE;MAC9BO,YAAY,EAAE,CAAC;MACf2D,QAAQ,EAAE,QAAQ;MAClB7B,EAAE,EAAE,CAAC;MACL7B,SAAS,EAAE;IACb,CAAE;IAAAQ,QAAA,gBAEA/B,OAAA,CAAC3B,GAAG;MAACsC,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,EAAE;QACP8G,IAAI,EAAE,EAAE;QACR1G,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE,YAAY;QACrBC,KAAK,EAAE,OAAO;QACdC,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,GAAG;QACPC,YAAY,EAAE,CAAC;QACfE,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE;MACP,CAAE;MAAAK,QAAA,gBACA/B,OAAA,CAAC3B,GAAG;QAACsC,EAAE,EAAE;UACP4C,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTlC,YAAY,EAAE,KAAK;UACnBJ,OAAO,EAAE,OAAO;UAChB0G,SAAS,EAAE;QACb;MAAE;QAAA3F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACLpC,OAAA,CAAC5B,UAAU;QAACiE,OAAO,EAAC,SAAS;QAAC1B,EAAE,EAAE;UAAE2B,UAAU,EAAE,GAAG;UAAEN,QAAQ,EAAE;QAAG,CAAE;QAAAD,QAAA,EAAC;MAErE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNpC,OAAA,CAAC3B,GAAG;MAACsC,EAAE,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAE2C,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAO,CAAE;MAAAzB,QAAA,EAC9DoF,UAAU,CAACrE,GAAG,CAAC,CAAC+E,IAAI,EAAEhE,KAAK,kBAC1B7D,OAAA,CAAC3B,GAAG;QAEFsC,EAAE,EAAE;UACFC,QAAQ,EAAE,UAAU;UACpBC,GAAG,EAAE,CAAC;UACN8G,IAAI,EAAE,CAAC;UACPpE,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdsE,OAAO,EAAEjE,KAAK,KAAK0C,YAAY,GAAG,CAAC,GAAG,CAAC;UACvC3E,UAAU,EAAE,0BAA0B;UACtCJ,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE;QACd,CAAE;QAAAM,QAAA,eAEF/B,OAAA,CAAC3B,GAAG;UAACsC,EAAE,EAAE;YACPC,QAAQ,EAAE,UAAU;YACpB2C,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACduE,UAAU,EAAE,0DAA0D5H,WAAW,CAAC0H,IAAI,CAACjC,KAAK,CAAC,GAAG;YAChGoC,cAAc,EAAE,OAAO;YACvBC,kBAAkB,EAAE,QAAQ;YAC5BzG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,UAAU;YACtB0B,CAAC,EAAE;UACL,CAAE;UAAApB,QAAA,eACA/B,OAAA,CAAC3B,GAAG;YAACsC,EAAE,EAAE;cAAEQ,KAAK,EAAE,OAAO;cAAEoC,KAAK,EAAE;YAAO,CAAE;YAAAxB,QAAA,gBACzC/B,OAAA,CAAC5B,UAAU;cAACiE,OAAO,EAAC,IAAI;cAAC1B,EAAE,EAAE;gBAC3B2B,UAAU,EAAE,GAAG;gBACfc,EAAE,EAAE,CAAC;gBACL8E,UAAU,EAAE,6BAA6B;gBACzClG,QAAQ,EAAE;kBAAElB,EAAE,EAAE,EAAE;kBAAEqH,EAAE,EAAE,EAAE;kBAAEpH,EAAE,EAAE;gBAAG;cACrC,CAAE;cAAAgB,QAAA,EACC8F,IAAI,CAACxC;YAAK;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACbpC,OAAA,CAAC5B,UAAU;cAACiE,OAAO,EAAC,SAAS;cAAC1B,EAAE,EAAE;gBAChCQ,KAAK,EAAE,UAAU;gBACjB+G,UAAU,EAAE,6BAA6B;gBACzClG,QAAQ,EAAE;kBAAElB,EAAE,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAG;cAC7B,CAAE;cAAAgB,QAAA,GACCqC,UAAU,CAACyD,IAAI,CAACvD,IAAI,CAAC,EAAC,UAAG,EAACuD,IAAI,CAACtC,QAAQ;YAAA;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAzCDyF,IAAI,CAAC3B,EAAE,IAAIrC,KAAK;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA0ClB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL+E,UAAU,CAACtE,MAAM,GAAG,CAAC,iBACpB7C,OAAA,CAAAE,SAAA;MAAA6B,QAAA,gBACE/B,OAAA,CAACV,UAAU;QACTyE,OAAO,EAAE0D,UAAW;QACpB9G,EAAE,EAAE;UACFC,QAAQ,EAAE,UAAU;UACpB+G,IAAI,EAAE,CAAC;UACP9G,GAAG,EAAE,KAAK;UACVc,SAAS,EAAE,kBAAkB;UAC7BR,KAAK,EAAE,OAAO;UACd,SAAS,EAAE;YAAED,OAAO,EAAE;UAAkB,CAAC;UACzCD,MAAM,EAAE;QACV,CAAE;QAAAc,QAAA,eAEF/B,OAAA,CAACH,kBAAkB;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACbpC,OAAA,CAACV,UAAU;QACTyE,OAAO,EAAEyD,UAAW;QACpB7G,EAAE,EAAE;UACFC,QAAQ,EAAE,UAAU;UACpBI,KAAK,EAAE,CAAC;UACRH,GAAG,EAAE,KAAK;UACVc,SAAS,EAAE,kBAAkB;UAC7BR,KAAK,EAAE,OAAO;UACd,SAAS,EAAE;YAAED,OAAO,EAAE;UAAkB,CAAC;UACzCD,MAAM,EAAE;QACV,CAAE;QAAAc,QAAA,eAEF/B,OAAA,CAACJ,gBAAgB;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA,eACb,CACH,EAGA+E,UAAU,CAACtE,MAAM,GAAG,CAAC,iBACpB7C,OAAA,CAAC3B,GAAG;MAACsC,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpBwH,MAAM,EAAE,EAAE;QACVT,IAAI,EAAE,KAAK;QACXhG,SAAS,EAAE,kBAAkB;QAC7BH,OAAO,EAAE,MAAM;QACfE,GAAG,EAAE,CAAC;QACNT,MAAM,EAAE;MACV,CAAE;MAAAc,QAAA,EACCoF,UAAU,CAACrE,GAAG,CAAC,CAACuF,CAAC,EAAExE,KAAK,kBACvB7D,OAAA,CAAC3B,GAAG;QAEF0F,OAAO,EAAEA,CAAA,KAAM;UACbyC,eAAe,CAAC3C,KAAK,CAAC;UACtB6C,WAAW,CAAC,KAAK,CAAC;QACpB,CAAE;QACF/F,EAAE,EAAE;UACF4C,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTlC,YAAY,EAAE,KAAK;UACnBJ,OAAO,EAAE2C,KAAK,KAAK0C,YAAY,GAAG,OAAO,GAAG,uBAAuB;UACnEvC,MAAM,EAAE,SAAS;UACjBpC,UAAU,EAAE;QACd;MAAE,GAZGiC,KAAK;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAaX,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACkE,EAAA,CAlOQF,kBAAkB;AAAAkC,GAAA,GAAlBlC,kBAAkB;AAoO3B,SAASmC,QAAQA,CAAC;EAAEV,IAAI;EAAEW,QAAQ;EAAEC,QAAQ;EAAEC,UAAU;EAAEC,YAAY;EAAEtG,OAAO;EAAEuG,OAAO;EAAEC,UAAU;EAAEC,YAAY;EAAEC;AAAY,CAAC,EAAE;EACjI,MAAMC,WAAW,GAAG3G,OAAO,KAAK,SAAS,GAAG,QAAQ,GAAG,MAAM;EAE7D,MAAM4G,eAAe,GAAIjD,CAAC,IAAK;IAC7B;IACA,IAAIA,CAAC,CAACC,MAAM,CAACiD,OAAO,CAAC,QAAQ,CAAC,IAAIlD,CAAC,CAACC,MAAM,CAACiD,OAAO,CAAC,iBAAiB,CAAC,EAAE;MACrE;IACF;IACAH,WAAW,IAAIA,WAAW,CAAClB,IAAI,CAAC3B,EAAE,CAAC;EACrC,CAAC;EAED,oBACElG,OAAA,CAACvB,IAAI;IACHkC,EAAE,EAAE;MACFa,OAAO,EAAE,MAAM;MACf2H,aAAa,EAAE,QAAQ;MACvB3F,MAAM,EAAE,MAAM;MACd5C,QAAQ,EAAE,UAAU;MACpBkB,QAAQ,EAAE;QAAEhB,EAAE,EAAE,MAAM;QAAEqH,EAAE,EAAE,GAAG;QAAEpH,EAAE,EAAE;MAAI,CAAC;MAC1Cc,QAAQ,EAAE;QAAEf,EAAE,EAAE,OAAO;QAAEqH,EAAE,EAAE,GAAG;QAAEpH,EAAE,EAAE;MAAI,CAAC;MAC3CqI,EAAE,EAAE,MAAM;MACV9H,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,CAAC;MACZ4B,CAAC,EAAE,CAAC;MACJjC,OAAO,EAAE,MAAM;MACfU,UAAU,EAAE,yDAAyD;MACrE,SAAS,EAAE;QACTD,SAAS,EAAE,kBAAkB;QAC7BJ,SAAS,EAAE;MACb;IACF,CAAE;IAAAQ,QAAA,gBAGF/B,OAAA,CAAC3B,GAAG;MACF0F,OAAO,EAAEkF,eAAgB;MACzBtI,EAAE,EAAE;QACFC,QAAQ,EAAE,UAAU;QACpB2C,KAAK,EAAE,MAAM;QACb8F,UAAU,EAAEL,WAAW;QACvB1H,YAAY,EAAE,CAAC;QACf2D,QAAQ,EAAE,QAAQ;QAClBjB,MAAM,EAAE;MACV,CAAE;MAAAjC,QAAA,eAEF/B,OAAA,CAACrB,SAAS;QACR+G,SAAS,EAAC,KAAK;QACf/E,EAAE,EAAE;UAAEC,QAAQ,EAAE,UAAU;UAAEC,GAAG,EAAE,CAAC;UAAE8G,IAAI,EAAE,CAAC;UAAEpE,KAAK,EAAE,MAAM;UAAEC,MAAM,EAAE,MAAM;UAAEsC,SAAS,EAAE;QAAQ,CAAE;QACjGF,KAAK,EAAEzF,WAAW,CAAC0H,IAAI,CAACjC,KAAK,CAAE;QAC/BC,GAAG,EAAEgC,IAAI,CAACxC,KAAM;QAChBU,OAAO,EAAGC,CAAC,IAAK;UAAEA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAGtD,OAAO,KAAK,SAAS,GAAG,6CAA6C,GAAG,2CAA2C;QAAE;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENpC,OAAA,CAAC3B,GAAG;MAACsC,EAAE,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEI,KAAK,EAAE,EAAE;QAAEoH,MAAM,EAAE,EAAE;QAAEnH,MAAM,EAAE,CAAC;QAAEqI,EAAE,EAAE,CAAC;QAAE9H,OAAO,EAAE,MAAM;QAAEE,GAAG,EAAE;MAAE,CAAE;MAAAK,QAAA,gBAClG/B,OAAA,CAACT,OAAO;QAAC8F,KAAK,EAAEyD,YAAY,GAAG,qBAAqB,GAAG,oBAAqB;QAAA/G,QAAA,eAC1E/B,OAAA,CAACV,UAAU;UACT6B,KAAK,EAAE2H,YAAY,GAAG,SAAS,GAAG,SAAU;UAC5CS,IAAI,EAAC,QAAQ;UACbxF,OAAO,EAAGiC,CAAC,IAAK;YACdA,CAAC,CAACwD,eAAe,CAAC,CAAC,CAAC,CAAC;YACrBX,UAAU,CAAChB,IAAI,CAAC;UAClB,CAAE;UACFlH,EAAE,EAAE;YACFiB,UAAU,EAAE,eAAe;YAC3B,SAAS,EAAE;cAAED,SAAS,EAAE;YAAa;UACvC,CAAE;UAAAI,QAAA,EAED+G,YAAY,gBACX9I,OAAA,CAACZ,iBAAiB;YAAC4C,QAAQ,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEvCpC,OAAA,CAACb,eAAe;YAAC6C,QAAQ,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACrC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACVpC,OAAA,CAACT,OAAO;QAAC8F,KAAK,EAAC,SAAS;QAAAtD,QAAA,eACtB/B,OAAA,CAACV,UAAU;UACT6B,KAAK,EAAC,SAAS;UACfoI,IAAI,EAAC,QAAQ;UACbxF,OAAO,EAAGiC,CAAC,IAAK;YACdA,CAAC,CAACwD,eAAe,CAAC,CAAC,CAAC,CAAC;YACrBZ,OAAO,CAACf,IAAI,CAAC;UACf,CAAE;UAAA9F,QAAA,eAEF/B,OAAA,CAACd,SAAS;YAAC8C,QAAQ,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAENpC,OAAA,CAACtB,WAAW;MACVqF,OAAO,EAAEkF,eAAgB;MACzBtI,EAAE,EAAE;QACFoE,IAAI,EAAE,CAAC;QACPvD,OAAO,EAAE,MAAM;QACf2H,aAAa,EAAE,QAAQ;QACvBM,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACL1F,MAAM,EAAE;MACV,CAAE;MAAAjC,QAAA,gBAEF/B,OAAA,CAAC5B,UAAU;QAACiE,OAAO,EAAC,IAAI;QAAC1B,EAAE,EAAE;UAAE2B,UAAU,EAAE,GAAG;UAAEnB,KAAK,EAAE,cAAc;UAAEiC,EAAE,EAAE,GAAG;UAAEpB,QAAQ,EAAE;YAAElB,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG;QAAE,CAAE;QAAAgB,QAAA,EAAE8F,IAAI,CAACxC;MAAK;QAAApD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACzIpC,OAAA,CAAC5B,UAAU;QAACiE,OAAO,EAAC,SAAS;QAAC1B,EAAE,EAAE;UAAEQ,KAAK,EAAE,UAAU;UAAEiC,EAAE,EAAE,CAAC;UAAEpB,QAAQ,EAAE;YAAElB,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG;QAAE,CAAE;QAAAgB,QAAA,GAAC,YAClF,eAAA/B,OAAA;UAAM4G,KAAK,EAAE;YAAEtE,UAAU,EAAE,GAAG;YAAEnB,KAAK,EAAE;UAAU,CAAE;UAAAY,QAAA,EAAE8F,IAAI,CAACvC,aAAa,IAAIuC,IAAI,CAACtC,QAAQ,IAAI;QAAM;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,YAAQ,EAACsG,UAAU,CAACb,IAAI,CAACrC,UAAU,IAAIqC,IAAI,CAACvD,IAAI,CAAC;MAAA;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3J,CAAC,eACbpC,OAAA,CAAC5B,UAAU;QAACiE,OAAO,EAAC,OAAO;QAAC1B,EAAE,EAAE;UAAEQ,KAAK,EAAE,UAAU;UAAEiC,EAAE,EAAE,CAAC;UAAE2B,IAAI,EAAE,CAAC;UAAE/C,QAAQ,EAAE;YAAElB,EAAE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAG;QAAE,CAAE;QAAAgB,QAAA,EACjGyG,QAAQ,GAAGX,IAAI,CAAC8B,WAAW,GAAGhB,YAAY,CAACd,IAAI,CAAC8B,WAAW;MAAC;QAAA1H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,EACZyF,IAAI,CAAC8B,WAAW,CAAC9G,MAAM,GAAG,GAAG,iBAC5B7C,OAAA,CAAC1B,MAAM;QACLiL,IAAI,EAAC,OAAO;QACZpI,KAAK,EAAC,SAAS;QACf4C,OAAO,EAAGiC,CAAC,IAAK;UACdA,CAAC,CAACwD,eAAe,CAAC,CAAC,CAAC,CAAC;UACrBf,QAAQ,CAAC,CAAC;QACZ,CAAE;QACF9H,EAAE,EAAE;UAAEiJ,SAAS,EAAE,YAAY;UAAEC,aAAa,EAAE,MAAM;UAAEvH,UAAU,EAAE,GAAG;UAAEgH,EAAE,EAAE;QAAE,CAAE;QAAAvH,QAAA,EAE9EyG,QAAQ,GAAG,oBAAoB,GAAG;MAAmB;QAAAvG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX;AAAC0H,GAAA,GA1HQvB,QAAQ;AA4HjB,SAASwB,gBAAgBA,CAAC;EAAE1D,MAAM;EAAElC,QAAQ;EAAE6F,WAAW;EAAErH,cAAc;EAAEF,gBAAgB;EAAEC,mBAAmB;EAAEuH,aAAa;EAAEC,gBAAgB;EAAEC,OAAO;EAAEC,SAAS;EAAEC,YAAY;EAAEC,aAAa;EAAEC,WAAW;EAAEC,kBAAkB;EAAEC,WAAW;EAAE5B,UAAU;EAAE6B,cAAc;EAAEC,aAAa;EAAEC,qBAAqB;EAAE7B;AAAY,CAAC,EAAE;EACjU,MAAMJ,YAAY,GAAGA,CAACkC,IAAI,EAAEC,SAAS,GAAG,GAAG,KAAK;IAC9C,IAAID,IAAI,CAAChI,MAAM,IAAIiI,SAAS,EAAE,OAAOD,IAAI;IACzC,OAAOA,IAAI,CAACE,SAAS,CAAC,CAAC,EAAED,SAAS,CAAC,GAAG,KAAK;EAC7C,CAAC;EACD,MAAMpC,UAAU,GAAIrE,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAAC0G,kBAAkB,CAAC,OAAO,EAAE;MAAEC,GAAG,EAAE,SAAS;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAU,CAAC,CAAC;EAC7F,CAAC;EACD,MAAMC,YAAY,GAAGjH,QAAQ,CAACkH,MAAM,CAACxD,IAAI,IACvCpF,gBAAgB,KAAK,OAAO,IAC5BoF,IAAI,CAACtC,QAAQ,KAAK9C,gBAAgB,IAClCoF,IAAI,CAACvC,aAAa,KAAK7C,gBACzB,CAAC,CAACmC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACb,oBACE5E,OAAA,CAAC3B,GAAG;IAACsC,EAAE,EAAE;MAAE2K,SAAS,EAAE,OAAO;MAAEpK,OAAO,EAAE,SAAS;MAAEqC,KAAK,EAAE,OAAO;MAAE0B,QAAQ,EAAE;IAAS,CAAE;IAAAlD,QAAA,gBAEtF/B,OAAA,CAAC9B,MAAM;MAAC0C,QAAQ,EAAC,OAAO;MAACO,KAAK,EAAC,SAAS;MAACoK,SAAS,EAAE,CAAE;MAAC5K,EAAE,EAAE;QAAEO,OAAO,EAAE,MAAM;QAAEsK,YAAY,EAAE,CAAC;QAAElI,WAAW,EAAE,UAAU;QAAErC,MAAM,EAAE;MAAK,CAAE;MAAAc,QAAA,eACrI/B,OAAA,CAAC7B,OAAO;QAACwC,EAAE,EAAE;UAAE2K,SAAS,EAAE,EAAE;UAAElK,EAAE,EAAE;QAAE,CAAE;QAAAW,QAAA,gBACpC/B,OAAA,CAAC3B,GAAG;UAACsC,EAAE,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEgK,QAAQ,EAAE;UAAE,CAAE;UAAA1J,QAAA,gBAC9D/B,OAAA,CAACpB,MAAM;YACL+G,GAAG,EAAEU,MAAM,CAACqF,IAAK;YACjB7F,GAAG,EAAC,MAAM;YACVlF,EAAE,EAAE;cAAE4C,KAAK,EAAE,EAAE;cAAEC,MAAM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACrCsC,OAAO,EAAGC,CAAC,IAAK;cAAEA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,cAAc;YAAE;UAAE;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACFpC,OAAA,CAAC5B,UAAU;YAACiE,OAAO,EAAC,IAAI;YAAC1B,EAAE,EAAE;cAAE2B,UAAU,EAAE,GAAG;cAAEnB,KAAK,EAAE,cAAc;cAAEa,QAAQ,EAAE;YAAG,CAAE;YAAAD,QAAA,EAAEsE,MAAM,CAAChB;UAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CAAC,eACNpC,OAAA,CAACV,UAAU;UAACqM,IAAI,EAAC,KAAK;UAACxK,KAAK,EAAC,SAAS;UAAC4C,OAAO,EAAE4G,aAAc;UAAChK,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC3E/B,OAAA,CAACL,UAAU;YAACqC,QAAQ,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACbpC,OAAA,CAACV,UAAU;UAACqM,IAAI,EAAC,KAAK;UAACxK,KAAK,EAAC,SAAS;UAAC4C,OAAO,EAAEuG,aAAc;UAAAvI,QAAA,eAC5D/B,OAAA,CAAClB,QAAQ;YAACkD,QAAQ,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAETpC,OAAA,CAAC/B,MAAM;MACL2N,MAAM,EAAC,OAAO;MACdC,IAAI,EAAEtB,WAAY;MAClB7J,OAAO,EAAE8J,kBAAmB;MAC5BsB,UAAU,EAAE;QAAEC,WAAW,EAAE;MAAK,CAAE;MAClCpL,EAAE,EAAE;QAAEM,MAAM,EAAE;MAAK,CAAE;MAAAc,QAAA,eAErB/B,OAAA,CAAC3B,GAAG;QAACsC,EAAE,EAAE;UAAE4C,KAAK,EAAE,GAAG;UAAEJ,CAAC,EAAE,CAAC;UAAEvC,QAAQ,EAAE;QAAW,CAAE;QAAAmB,QAAA,gBAClD/B,OAAA,CAACV,UAAU;UACTyE,OAAO,EAAEyG,kBAAmB;UAC5B7J,EAAE,EAAE;YAAEC,QAAQ,EAAE,UAAU;YAAEC,GAAG,EAAE,CAAC;YAAEG,KAAK,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAG,CAAE;UAC3D,cAAW,OAAO;UAAAc,QAAA,eAElB/B,OAAA,CAACR,SAAS;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACbpC,OAAA,CAAC3B,GAAG;UAACsC,EAAE,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAE2B,EAAE,EAAE;UAAE,CAAE;UAAArB,QAAA,gBACxD/B,OAAA,CAACpB,MAAM;YAAC+G,GAAG,EAAEU,MAAM,CAACqF,IAAK;YAAC7F,GAAG,EAAC,MAAM;YAAClF,EAAE,EAAE;cAAE4C,KAAK,EAAE,EAAE;cAAEC,MAAM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAACsC,OAAO,EAAGC,CAAC,IAAK;cAAEA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,cAAc;YAAE;UAAE;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjIpC,OAAA,CAAC5B,UAAU;YAACiE,OAAO,EAAC,IAAI;YAAC1B,EAAE,EAAE;cAAE2B,UAAU,EAAE,GAAG;cAAEnB,KAAK,EAAE,cAAc;cAAEa,QAAQ,EAAE;YAAG,CAAE;YAAAD,QAAA,EAAEsE,MAAM,CAAChB;UAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CAAC,eACNpC,OAAA,CAACnB,OAAO;UAAC8B,EAAE,EAAE;YAAEyC,EAAE,EAAE;UAAE;QAAE;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1BpC,OAAA,CAACxB,KAAK;UAACqG,OAAO,EAAE,CAAE;UAAA9C,QAAA,gBAChB/B,OAAA,CAAC1B,MAAM;YAAC+D,OAAO,EAAC,WAAW;YAAClB,KAAK,EAAC,SAAS;YAAC6K,SAAS;YAACrL,EAAE,EAAE;cAAEkJ,aAAa,EAAE;YAAO,CAAE;YAAA9H,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtGpC,OAAA,CAAC1B,MAAM;YAAC+D,OAAO,EAAC,UAAU;YAAClB,KAAK,EAAC,SAAS;YAAC6K,SAAS;YAACrL,EAAE,EAAE;cAAEkJ,aAAa,EAAE;YAAO,CAAE;YAAA9H,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETpC,OAAA,CAAC3B,GAAG;MAACsC,EAAE,EAAE;QAAEO,OAAO,EAAE,MAAM;QAAEsK,YAAY,EAAE,CAAC;QAAElI,WAAW,EAAE,UAAU;QAAEjC,EAAE,EAAE,CAAC;QAAET,QAAQ,EAAE,OAAO;QAAEC,GAAG,EAAE,EAAE;QAAE8G,IAAI,EAAE,CAAC;QAAE3G,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAK,CAAE;MAAAc,QAAA,eACzI/B,OAAA,CAACxB,KAAK;QAACyN,SAAS,EAAC,KAAK;QAACpH,OAAO,EAAE,CAAE;QAAClE,EAAE,EAAE;UAAEuL,SAAS,EAAE,MAAM;UAAE9K,EAAE,EAAE,CAAC;UAAE,sBAAsB,EAAE;YAAEI,OAAO,EAAE;UAAO,CAAC;UAAE2K,cAAc,EAAE,MAAM;UAAEC,eAAe,EAAE;QAAO,CAAE;QAAArK,QAAA,EAC/JzB,UAAU,CAACwC,GAAG,CAAEC,GAAG,iBAClB/C,OAAA,CAACzB,IAAI;UAEHuF,KAAK,EAAEf,GAAI;UACX5B,KAAK,EAAEsB,gBAAgB,KAAKM,GAAG,GAAG,SAAS,GAAG,SAAU;UACxDgB,OAAO,EAAEA,CAAA,KAAMrB,mBAAmB,CAACK,GAAG,CAAE;UACxCpC,EAAE,EAAE;YAAE2B,UAAU,EAAE,GAAG;YAAE0B,MAAM,EAAE,SAAS;YAAEyB,UAAU,EAAE,CAAC;YAAE4G,UAAU,EAAE;UAAS;QAAE,GAJ3EtJ,GAAG;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKT,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENpC,OAAA,CAAC3B,GAAG;MAACsC,EAAE,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEC,GAAG,EAAE,GAAG;QAAE8G,IAAI,EAAE,CAAC;QAAE3G,KAAK,EAAE,CAAC;QAAEoH,MAAM,EAAE,EAAE;QAAEhH,EAAE,EAAE,CAAC;QAAES,QAAQ,EAAE,MAAM;QAAEuH,EAAE,EAAE,MAAM;QAAE7F,KAAK,EAAE,MAAM;QAAE/B,OAAO,EAAE,MAAM;QAAE2H,aAAa,EAAE,QAAQ;QAAEmC,SAAS,EAAE,CAAC;QAAErK,MAAM,EAAE,CAAC;QAAEyI,EAAE,EAAE,CAAC;QAAED,EAAE,EAAE;MAAE,CAAE;MAAA1H,QAAA,eAC9M/B,OAAA,CAAC3B,GAAG;QACFqH,SAAS,EAAC,MAAM;QAChB/E,EAAE,EAAE;UACFoE,IAAI,EAAE,CAAC;UACPvB,MAAM,EAAE,4BAA4B;UAAE;UACtC8I,SAAS,EAAE,MAAM;UACjBhB,SAAS,EAAE,CAAC;UACZ5B,EAAE,EAAE,CAAC;UACLD,EAAE,EAAE,CAAC;UACL,sBAAsB,EAAE;YAAEjI,OAAO,EAAE;UAAO,CAAC;UAC3C2K,cAAc,EAAE,MAAM;UACtBC,eAAe,EAAE;QACnB,CAAE;QAAArK,QAAA,gBAGF/B,OAAA,CAACoG,kBAAkB;UAACjC,QAAQ,EAAEA,QAAS;UAACkC,MAAM,EAAEA;QAAO;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1DpC,OAAA,CAACwC,WAAW;UAACC,gBAAgB,EAAEA,gBAAiB;UAACC,mBAAmB,EAAEA,mBAAoB;UAACC,cAAc,EAAEA;QAAe;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG7HpC,OAAA,CAACkE,WAAW;UAACC,QAAQ,EAAEA;QAAS;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEnCpC,OAAA,CAAC3B,GAAG;UACFsC,EAAE,EAAE;YACFa,OAAO,EAAE,MAAM;YACf2H,aAAa,EAAE,QAAQ;YACvBzH,GAAG,EAAE,CAAC;YACN4K,SAAS,EAAE,MAAM;YACjB7C,EAAE,EAAE;cAAE3I,EAAE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAC;YAAE;YACvB,sBAAsB,EAAE;cAAES,OAAO,EAAE;YAAO,CAAC;YAC3C2K,cAAc,EAAE,MAAM;YACtBC,eAAe,EAAE,MAAM;YACvBhL,EAAE,EAAE,CAAC;YACLmL,EAAE,EAAE,CAAC,CAAE;UACT,CAAE;UAAAxK,QAAA,EAEDoI,OAAO,GACNqC,KAAK,CAACC,IAAI,CAAC;YAAE5J,MAAM,EAAE;UAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACuF,CAAC,EAAEqE,GAAG,kBACnC1M,OAAA,CAACvB,IAAI;YAAWkC,EAAE,EAAE;cAAEa,OAAO,EAAE,MAAM;cAAE2H,aAAa,EAAE,QAAQ;cAAE3F,MAAM,EAAE,MAAM;cAAED,KAAK,EAAE;YAAO,CAAE;YAAAxB,QAAA,gBAC9F/B,OAAA,CAAC3B,GAAG;cAACsC,EAAE,EAAE;gBAAEC,QAAQ,EAAE,UAAU;gBAAE2C,KAAK,EAAE,MAAM;gBAAE8F,UAAU,EAAE,MAAM;gBAAEnI,OAAO,EAAE;cAAW;YAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7FpC,OAAA,CAACtB,WAAW;cAACiC,EAAE,EAAE;gBAAEoE,IAAI,EAAE,CAAC;gBAAEvD,OAAO,EAAE,MAAM;gBAAE2H,aAAa,EAAE,QAAQ;gBAAEM,EAAE,EAAE;cAAE,CAAE;cAAA1H,QAAA,gBAC5E/B,OAAA,CAAC3B,GAAG;gBAACsC,EAAE,EAAE;kBAAE6C,MAAM,EAAE,EAAE;kBAAEtC,OAAO,EAAE,UAAU;kBAAEkC,EAAE,EAAE,CAAC;kBAAE9B,YAAY,EAAE;gBAAE;cAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxEpC,OAAA,CAAC3B,GAAG;gBAACsC,EAAE,EAAE;kBAAE6C,MAAM,EAAE,EAAE;kBAAEtC,OAAO,EAAE,UAAU;kBAAEkC,EAAE,EAAE,CAAC;kBAAE9B,YAAY,EAAE;gBAAE;cAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxEpC,OAAA,CAAC3B,GAAG;gBAACsC,EAAE,EAAE;kBAAE6C,MAAM,EAAE,EAAE;kBAAEtC,OAAO,EAAE,UAAU;kBAAEI,YAAY,EAAE;gBAAE;cAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA,GANLsK,GAAG;YAAAzK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOR,CACP,CAAC,GACAgJ,YAAY,CAACvI,MAAM,KAAK,CAAC,gBAC3B7C,OAAA,CAAC3B,GAAG;YAACsC,EAAE,EAAE;cAAE+G,SAAS,EAAE,QAAQ;cAAErG,EAAE,EAAE,CAAC;cAAEF,KAAK,EAAE,UAAU;cAAEoC,KAAK,EAAE;YAAO,CAAE;YAAAxB,QAAA,gBACxE/B,OAAA,CAAC5B,UAAU;cAACiE,OAAO,EAAC,IAAI;cAAAN,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACtDpC,OAAA,CAAC5B,UAAU;cAACiE,OAAO,EAAC,OAAO;cAAAN,QAAA,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,GAENgJ,YAAY,CAACtI,GAAG,CAAC,CAAC+E,IAAI,EAAE6E,GAAG,kBACzB1M,OAAA,CAAC3B,GAAG;YAAsBsC,EAAE,EAAE;cAAE4C,KAAK,EAAE;YAAO,CAAE;YAAAxB,QAAA,eAC9C/B,OAAA,CAACuI,QAAQ;cACPV,IAAI,EAAEA,IAAK;cACXW,QAAQ,EAAE,CAAC,CAACyB,aAAa,CAACpC,IAAI,CAAC3B,EAAE,CAAE;cACnCuC,QAAQ,EAAEA,CAAA,KAAMyB,gBAAgB,CAACrC,IAAI,CAAC3B,EAAE,CAAE;cAC1CwC,UAAU,EAAEA,UAAW;cACvBC,YAAY,EAAEA,YAAa;cAC3BtG,OAAO,EAAC,QAAQ;cAChBuG,OAAO,EAAE6B,WAAY;cACrB5B,UAAU,EAAEA,UAAW;cACvBC,YAAY,EAAE4B,cAAc,CAACiC,GAAG,CAAC9E,IAAI,CAAC3B,EAAE,CAAE;cAC1C6C,WAAW,EAAEA;YAAY;cAAA9G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC,GAZMyF,IAAI,CAAC3B,EAAE,IAAIwG,GAAG;YAAAzK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAanB,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpC,OAAA,CAAC3B,GAAG;MAACsC,EAAE,EAAE;QACPC,QAAQ,EAAE,OAAO;QACjB+G,IAAI,EAAE,CAAC;QACP3G,KAAK,EAAE,CAAC;QACRoH,MAAM,EAAE,CAAC;QACTnH,MAAM,EAAE,IAAI;QACZO,OAAO,EAAE,OAAO;QAChBoL,eAAe,EAAE,OAAO;QACxBC,SAAS,EAAE,mBAAmB;QAC9BtL,SAAS,EAAE;MACb,CAAE;MAAAQ,QAAA,eACA/B,OAAA,CAAC3B,GAAG;QAACsC,EAAE,EAAE;UACPa,OAAO,EAAE,MAAM;UACfmC,cAAc,EAAE,cAAc;UAC9BlC,UAAU,EAAE,QAAQ;UACpB+B,MAAM,EAAE,EAAE;UACVpC,EAAE,EAAE;QACN,CAAE;QAAAW,QAAA,gBACA/B,OAAA,CAAC3B,GAAG;UACF0F,OAAO,EAAEA,CAAA,KAAM6G,qBAAqB,CAAC,CAAC,CAAE;UACxCkC,SAAS,EAAE,mBAAmB1C,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAArI,QAAA,gBAEhE/B,OAAA;YAAG8M,SAAS,EAAE,+BAA+B1C,SAAS,KAAK,CAAC,GAAG,eAAe,GAAG,eAAe;UAAG;YAAAnI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxGpC,OAAA,CAAC5B,UAAU;YAACiE,OAAO,EAAC,SAAS;YAACyK,SAAS,EAAC,kBAAkB;YAACnM,EAAE,EAAE;cAAEQ,KAAK,EAAEiJ,SAAS,KAAK,CAAC,GAAG,cAAc,GAAG;YAAiB,CAAE;YAAArI,QAAA,EAAC;UAE/H;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENpC,OAAA,CAAC3B,GAAG;UACF0F,OAAO,EAAEA,CAAA,KAAM6G,qBAAqB,CAAC,CAAC,CAAE;UACxCkC,SAAS,EAAE,mBAAmB1C,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAArI,QAAA,gBAEhE/B,OAAA;YAAG8M,SAAS,EAAE,iCAAiC1C,SAAS,KAAK,CAAC,GAAG,eAAe,GAAG,eAAe;UAAG;YAAAnI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1GpC,OAAA,CAAC5B,UAAU;YAACiE,OAAO,EAAC,SAAS;YAACyK,SAAS,EAAC,kBAAkB;YAACnM,EAAE,EAAE;cAAEQ,KAAK,EAAEiJ,SAAS,KAAK,CAAC,GAAG,cAAc,GAAG;YAAiB,CAAE;YAAArI,QAAA,EAAC;UAE/H;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENpC,OAAA,CAAC3B,GAAG;UACF0F,OAAO,EAAEA,CAAA,KAAMgJ,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAS;UAC/CH,SAAS,EAAE,mBAAmB1C,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAArI,QAAA,gBAEhE/B,OAAA;YAAG8M,SAAS,EAAE,mCAAmC1C,SAAS,KAAK,CAAC,GAAG,eAAe,GAAG,eAAe;UAAG;YAAAnI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5GpC,OAAA,CAAC5B,UAAU;YAACiE,OAAO,EAAC,SAAS;YAACyK,SAAS,EAAC,kBAAkB;YAACnM,EAAE,EAAE;cAAEQ,KAAK,EAAEiJ,SAAS,KAAK,CAAC,GAAG,cAAc,GAAG;YAAiB,CAAE;YAAArI,QAAA,EAAC;UAE/H;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC8K,GAAA,GA7MQnD,gBAAgB;AA+MzB,SAASoD,iBAAiBA,CAAC;EAAE9G,MAAM;EAAElC,QAAQ;EAAE6F,WAAW;EAAErH,cAAc;EAAEF,gBAAgB;EAAEC,mBAAmB;EAAEuH,aAAa;EAAEC,gBAAgB;EAAEC,OAAO;EAAEM,WAAW;EAAE5B,UAAU;EAAE6B,cAAc;EAAEC,aAAa;EAAEJ,WAAW;EAAED,aAAa;EAAEE,kBAAkB;EAAEzB;AAAY,CAAC,EAAE;EAClR,MAAMJ,YAAY,GAAGA,CAACkC,IAAI,EAAEC,SAAS,GAAG,GAAG,KAAK;IAC9C,IAAID,IAAI,CAAChI,MAAM,IAAIiI,SAAS,EAAE,OAAOD,IAAI;IACzC,OAAOA,IAAI,CAACE,SAAS,CAAC,CAAC,EAAED,SAAS,CAAC,GAAG,KAAK;EAC7C,CAAC;EACD,MAAMpC,UAAU,GAAIrE,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAAC0G,kBAAkB,CAAC,OAAO,EAAE;MAAEC,GAAG,EAAE,SAAS;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAU,CAAC,CAAC;EAC7F,CAAC;EACD,MAAMC,YAAY,GAAGjH,QAAQ,CAACkH,MAAM,CAACxD,IAAI,IACvCpF,gBAAgB,KAAK,OAAO,IAC5BoF,IAAI,CAACtC,QAAQ,KAAK9C,gBAAgB,IAClCoF,IAAI,CAACvC,aAAa,KAAK7C,gBACzB,CAAC,CAACmC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACb,oBACE5E,OAAA,CAAC3B,GAAG;IAACsC,EAAE,EAAE;MAAE2K,SAAS,EAAE,OAAO;MAAEpK,OAAO,EAAE,SAAS;MAAEqC,KAAK,EAAE,OAAO;MAAE0B,QAAQ,EAAE;IAAS,CAAE;IAAAlD,QAAA,gBACtF/B,OAAA,CAAC9B,MAAM;MAAC0C,QAAQ,EAAC,OAAO;MAACO,KAAK,EAAC,SAAS;MAACoK,SAAS,EAAE,CAAE;MAAC5K,EAAE,EAAE;QAAEO,OAAO,EAAE,MAAM;QAAEsK,YAAY,EAAE,CAAC;QAAElI,WAAW,EAAE,UAAU;QAAErC,MAAM,EAAE;MAAK,CAAE;MAAAc,QAAA,eACrI/B,OAAA,CAAC7B,OAAO;QAACwC,EAAE,EAAE;UAAE2K,SAAS,EAAE,EAAE;UAAElK,EAAE,EAAE;QAAE,CAAE;QAAAW,QAAA,gBACpC/B,OAAA,CAAC3B,GAAG;UAACsC,EAAE,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEgK,QAAQ,EAAE;UAAE,CAAE;UAAA1J,QAAA,gBAC9D/B,OAAA,CAACpB,MAAM;YAAC+G,GAAG,EAAEU,MAAM,CAACqF,IAAK;YAAC7F,GAAG,EAAC,MAAM;YAAClF,EAAE,EAAE;cAAE4C,KAAK,EAAE,EAAE;cAAEC,MAAM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAACsC,OAAO,EAAGC,CAAC,IAAK;cAAEA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,cAAc;YAAE;UAAE;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjIpC,OAAA,CAAC5B,UAAU;YAACiE,OAAO,EAAC,IAAI;YAAC1B,EAAE,EAAE;cAAE2B,UAAU,EAAE,GAAG;cAAEnB,KAAK,EAAE,cAAc;cAAEa,QAAQ,EAAE;YAAG,CAAE;YAAAD,QAAA,EAAEsE,MAAM,CAAChB;UAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CAAC,eACNpC,OAAA,CAACV,UAAU;UAACqM,IAAI,EAAC,KAAK;UAACxK,KAAK,EAAC,SAAS;UAAC4C,OAAO,EAAE4G,aAAc;UAAChK,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC3E/B,OAAA,CAACL,UAAU;YAACqC,QAAQ,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACbpC,OAAA,CAACV,UAAU;UAACqM,IAAI,EAAC,KAAK;UAACxK,KAAK,EAAC,SAAS;UAAC4C,OAAO,EAAEuG,aAAc;UAAC3J,EAAE,EAAE;YAAEyM,EAAE,EAAE;UAAE,CAAE;UAAArL,QAAA,eAC3E/B,OAAA,CAAClB,QAAQ;YAACkD,QAAQ,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACTpC,OAAA,CAAC/B,MAAM;MACL2N,MAAM,EAAC,OAAO;MACdC,IAAI,EAAEtB,WAAY;MAClB7J,OAAO,EAAE8J,kBAAmB;MAC5BsB,UAAU,EAAE;QAAEC,WAAW,EAAE;MAAK,CAAE;MAClCpL,EAAE,EAAE;QAAEM,MAAM,EAAE;MAAK,CAAE;MAAAc,QAAA,eAErB/B,OAAA,CAAC3B,GAAG;QAACsC,EAAE,EAAE;UAAE4C,KAAK,EAAE,GAAG;UAAEJ,CAAC,EAAE,CAAC;UAAEvC,QAAQ,EAAE;QAAW,CAAE;QAAAmB,QAAA,gBAClD/B,OAAA,CAACV,UAAU;UACTyE,OAAO,EAAEyG,kBAAmB;UAC5B7J,EAAE,EAAE;YAAEC,QAAQ,EAAE,UAAU;YAAEC,GAAG,EAAE,CAAC;YAAEG,KAAK,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAG,CAAE;UAC3D,cAAW,OAAO;UAAAc,QAAA,eAElB/B,OAAA,CAACR,SAAS;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACbpC,OAAA,CAAC3B,GAAG;UAACsC,EAAE,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAE2B,EAAE,EAAE;UAAE,CAAE;UAAArB,QAAA,gBACxD/B,OAAA,CAACpB,MAAM;YAAC+G,GAAG,EAAEU,MAAM,CAACqF,IAAK;YAAC7F,GAAG,EAAC,MAAM;YAAClF,EAAE,EAAE;cAAE4C,KAAK,EAAE,EAAE;cAAEC,MAAM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAACsC,OAAO,EAAGC,CAAC,IAAK;cAAEA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,cAAc;YAAE;UAAE;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjIpC,OAAA,CAAC5B,UAAU;YAACiE,OAAO,EAAC,IAAI;YAAC1B,EAAE,EAAE;cAAE2B,UAAU,EAAE,GAAG;cAAEnB,KAAK,EAAE,cAAc;cAAEa,QAAQ,EAAE;YAAG,CAAE;YAAAD,QAAA,EAAEsE,MAAM,CAAChB;UAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CAAC,eACNpC,OAAA,CAACnB,OAAO;UAAC8B,EAAE,EAAE;YAAEyC,EAAE,EAAE;UAAE;QAAE;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1BpC,OAAA,CAACxB,KAAK;UAACqG,OAAO,EAAE,CAAE;UAAA9C,QAAA,gBAChB/B,OAAA,CAAC1B,MAAM;YAAC+D,OAAO,EAAC,WAAW;YAAClB,KAAK,EAAC,SAAS;YAAC6K,SAAS;YAACrL,EAAE,EAAE;cAAEkJ,aAAa,EAAE;YAAO,CAAE;YAAA9H,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtGpC,OAAA,CAAC1B,MAAM;YAAC+D,OAAO,EAAC,UAAU;YAAClB,KAAK,EAAC,SAAS;YAAC6K,SAAS;YAACrL,EAAE,EAAE;cAAEkJ,aAAa,EAAE;YAAO,CAAE;YAAA9H,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACTpC,OAAA,CAAC3B,GAAG;MAACsC,EAAE,EAAE;QAAEO,OAAO,EAAE,MAAM;QAAEsK,YAAY,EAAE,CAAC;QAAElI,WAAW,EAAE,UAAU;QAAEjC,EAAE,EAAE,CAAC;QAAET,QAAQ,EAAE,OAAO;QAAEC,GAAG,EAAE,EAAE;QAAE8G,IAAI,EAAE,CAAC;QAAE3G,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAK,CAAE;MAAAc,QAAA,eACzI/B,OAAA,CAAC3B,GAAG;QAACsC,EAAE,EAAE;UAAEkB,QAAQ,EAAE,QAAQ;UAAEuH,EAAE,EAAE,MAAM;UAAEhI,EAAE,EAAE;QAAE,CAAE;QAAAW,QAAA,eACjD/B,OAAA,CAACxB,KAAK;UAACyN,SAAS,EAAC,KAAK;UAACpH,OAAO,EAAE,CAAE;UAAClE,EAAE,EAAE;YACrCuL,SAAS,EAAE,MAAM;YACjBvI,cAAc,EAAE,QAAQ;YACxB,sBAAsB,EAAE;cAAEnC,OAAO,EAAE;YAAO,CAAC;YAC3C2K,cAAc,EAAE,MAAM;YACtBC,eAAe,EAAE;UACnB,CAAE;UAAArK,QAAA,EACCzB,UAAU,CAACwC,GAAG,CAAEC,GAAG,iBAClB/C,OAAA,CAACzB,IAAI;YAEHuF,KAAK,EAAEf,GAAI;YACX5B,KAAK,EAAEsB,gBAAgB,KAAKM,GAAG,GAAG,SAAS,GAAG,SAAU;YACxDgB,OAAO,EAAEA,CAAA,KAAMrB,mBAAmB,CAACK,GAAG,CAAE;YACxCpC,EAAE,EAAE;cAAE2B,UAAU,EAAE,GAAG;cAAE0B,MAAM,EAAE,SAAS;cAAEyB,UAAU,EAAE,CAAC;cAAE4G,UAAU,EAAE;YAAS;UAAE,GAJ3EtJ,GAAG;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKT,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNpC,OAAA,CAAC3B,GAAG;MAACsC,EAAE,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEC,GAAG,EAAE,GAAG;QAAE8G,IAAI,EAAE,CAAC;QAAE3G,KAAK,EAAE,CAAC;QAAEoH,MAAM,EAAE,CAAC;QAAEhH,EAAE,EAAE,CAAC;QAAES,QAAQ,EAAE,QAAQ;QAAEuH,EAAE,EAAE,MAAM;QAAE7F,KAAK,EAAE,MAAM;QAAE/B,OAAO,EAAE,MAAM;QAAE2H,aAAa,EAAE,KAAK;QAAEzH,GAAG,EAAE,CAAC;QAAE4J,SAAS,EAAE,CAAC;QAAErK,MAAM,EAAE,CAAC;QAAEyI,EAAE,EAAE,CAAC;QAAED,EAAE,EAAE;MAAE,CAAE;MAAA1H,QAAA,gBACpN/B,OAAA,CAAC3B,GAAG;QACFqH,SAAS,EAAC,MAAM;QAChB/E,EAAE,EAAE;UACFoE,IAAI,EAAE,CAAC;UACPvB,MAAM,EAAE,qBAAqB;UAAE;UAC/B8I,SAAS,EAAE,MAAM;UACjBhB,SAAS,EAAE,CAAC;UACZ5B,EAAE,EAAE,CAAC;UACLD,EAAE,EAAE,CAAC;UACL,sBAAsB,EAAE;YAAEjI,OAAO,EAAE;UAAO,CAAC;UAC3C2K,cAAc,EAAE,MAAM;UACtBC,eAAe,EAAE;QACnB,CAAE;QAAArK,QAAA,gBAGF/B,OAAA,CAACoG,kBAAkB;UAACjC,QAAQ,EAAEA,QAAS;UAACkC,MAAM,EAAEA;QAAO;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1DpC,OAAA,CAACwC,WAAW;UAACC,gBAAgB,EAAEA,gBAAiB;UAACC,mBAAmB,EAAEA,mBAAoB;UAACC,cAAc,EAAEA;QAAe;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG7HpC,OAAA,CAACkE,WAAW;UAACC,QAAQ,EAAEA;QAAS;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEnCpC,OAAA,CAAC3B,GAAG;UAACsC,EAAE,EAAE;YACPa,OAAO,EAAE,MAAM;YACf2H,aAAa,EAAE,KAAK;YACpBzH,GAAG,EAAE,CAAC;YACN6B,KAAK,EAAE,MAAM;YACb1B,QAAQ,EAAE,MAAM;YAChBqK,SAAS,EAAE,MAAM;YACjB,sBAAsB,EAAE;cAAE1K,OAAO,EAAE;YAAO,CAAC;YAC3C2K,cAAc,EAAE,MAAM;YACtBC,eAAe,EAAE,MAAM;YACvBhL,EAAE,EAAE,CAAC;YACLkI,EAAE,EAAE,CAAC;YACLG,EAAE,EAAE,CAAC,CAAE;UACT,CAAE;UAAA1H,QAAA,EACCoI,OAAO,GACNqC,KAAK,CAACC,IAAI,CAAC;YAAE5J,MAAM,EAAE;UAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACuF,CAAC,EAAEqE,GAAG,kBACnC1M,OAAA,CAACvB,IAAI;YAAWkC,EAAE,EAAE;cAAEa,OAAO,EAAE,MAAM;cAAE2H,aAAa,EAAE,QAAQ;cAAE3F,MAAM,EAAE,MAAM;cAAE1B,QAAQ,EAAE,GAAG;cAAE2D,UAAU,EAAE;YAAE,CAAE;YAAA1D,QAAA,gBAC7G/B,OAAA,CAAC3B,GAAG;cAACsC,EAAE,EAAE;gBAAEC,QAAQ,EAAE,UAAU;gBAAE2C,KAAK,EAAE,MAAM;gBAAE8F,UAAU,EAAE,MAAM;gBAAEnI,OAAO,EAAE;cAAW;YAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7FpC,OAAA,CAACtB,WAAW;cAACiC,EAAE,EAAE;gBAAEoE,IAAI,EAAE,CAAC;gBAAEvD,OAAO,EAAE,MAAM;gBAAE2H,aAAa,EAAE,QAAQ;gBAAEM,EAAE,EAAE;cAAE,CAAE;cAAA1H,QAAA,gBAC5E/B,OAAA,CAAC3B,GAAG;gBAACsC,EAAE,EAAE;kBAAE6C,MAAM,EAAE,EAAE;kBAAEtC,OAAO,EAAE,UAAU;kBAAEkC,EAAE,EAAE,CAAC;kBAAE9B,YAAY,EAAE;gBAAE;cAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxEpC,OAAA,CAAC3B,GAAG;gBAACsC,EAAE,EAAE;kBAAE6C,MAAM,EAAE,EAAE;kBAAEtC,OAAO,EAAE,UAAU;kBAAEkC,EAAE,EAAE,CAAC;kBAAE9B,YAAY,EAAE;gBAAE;cAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxEpC,OAAA,CAAC3B,GAAG;gBAACsC,EAAE,EAAE;kBAAE6C,MAAM,EAAE,EAAE;kBAAEtC,OAAO,EAAE,UAAU;kBAAEI,YAAY,EAAE;gBAAE;cAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA,GANLsK,GAAG;YAAAzK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOR,CACP,CAAC,GACAgJ,YAAY,CAACvI,MAAM,KAAK,CAAC,gBAC3B7C,OAAA,CAAC3B,GAAG;YAACsC,EAAE,EAAE;cAAE+G,SAAS,EAAE,QAAQ;cAAErG,EAAE,EAAE,CAAC;cAAEF,KAAK,EAAE,UAAU;cAAEoC,KAAK,EAAE;YAAO,CAAE;YAAAxB,QAAA,gBACxE/B,OAAA,CAAC5B,UAAU;cAACiE,OAAO,EAAC,IAAI;cAAAN,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACtDpC,OAAA,CAAC5B,UAAU;cAACiE,OAAO,EAAC,OAAO;cAAAN,QAAA,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,GAENgJ,YAAY,CAACtI,GAAG,CAAC,CAAC+E,IAAI,EAAE6E,GAAG,kBACzB1M,OAAA,CAAC3B,GAAG;YAAsBsC,EAAE,EAAE;cAAEmB,QAAQ,EAAE,GAAG;cAAE2D,UAAU,EAAE;YAAE,CAAE;YAAA1D,QAAA,eAC7C/B,OAAA,CAACuI,QAAQ;cACzBV,IAAI,EAAEA,IAAK;cACXW,QAAQ,EAAE,CAAC,CAACyB,aAAa,CAACpC,IAAI,CAAC3B,EAAE,CAAE;cACnCuC,QAAQ,EAAEA,CAAA,KAAMyB,gBAAgB,CAACrC,IAAI,CAAC3B,EAAE,CAAE;cAC1CwC,UAAU,EAAEA,UAAW;cACvBC,YAAY,EAAEA,YAAa;cAC3BtG,OAAO,EAAC,SAAS;cACjBuG,OAAO,EAAE6B,WAAY;cACrB5B,UAAU,EAAEA,UAAW;cACvBC,YAAY,EAAE4B,cAAc,CAACiC,GAAG,CAAC9E,IAAI,CAAC3B,EAAE,CAAE;cAC1C6C,WAAW,EAAEA;YAAY;cAAA9G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC,GAZQyF,IAAI,CAAC3B,EAAE,IAAIwG,GAAG;YAAAzK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAanB,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpC,OAAA,CAAC3B,GAAG;QAACqH,SAAS,EAAC,OAAO;QAAC/E,EAAE,EAAE;UAAE4C,KAAK,EAAE,GAAG;UAAE+F,EAAE,EAAE,CAAC;UAAE9H,OAAO,EAAE,OAAO;UAAE4B,EAAE,EAAE;QAAE,CAAE;QAAArB,QAAA,eACxE/B,OAAA,CAAC3B,GAAG;UAACsC,EAAE,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEI,YAAY,EAAE,CAAC;YAAEC,SAAS,EAAE,CAAC;YAAE4B,CAAC,EAAE,CAAC;YAAEC,EAAE,EAAE,CAAC;YAAEkG,EAAE,EAAE;UAAE,CAAE;UAAAvH,QAAA,gBAC9E/B,OAAA,CAAC5B,UAAU;YAACiE,OAAO,EAAC,IAAI;YAAC1B,EAAE,EAAE;cAAE2B,UAAU,EAAE,GAAG;cAAEnB,KAAK,EAAE,cAAc;cAAEiC,EAAE,EAAE;YAAE,CAAE;YAAArB,QAAA,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC3GpC,OAAA,CAACnB,OAAO;YAAC8B,EAAE,EAAE;cAAEyC,EAAE,EAAE;YAAE;UAAE;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1BpC,OAAA,CAACxB,KAAK;YAACqG,OAAO,EAAE,CAAE;YAAA9C,QAAA,EACfiI,WAAW,CAACnH,MAAM,GAAG,CAAC,GACrBmH,WAAW,CAAClH,GAAG,CAAC,CAACuK,IAAI,EAAEX,GAAG,kBACxB1M,OAAA,CAAC3B,GAAG;cAAsBsC,EAAE,EAAE;gBAAEa,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,GAAG,EAAE;cAAE,CAAE;cAAAK,QAAA,gBAC9E/B,OAAA,CAACpB,MAAM;gBAAC+G,GAAG,EAAExF,WAAW,CAACkN,IAAI,CAACzH,KAAK,CAAE;gBAACC,GAAG,EAAEwH,IAAI,CAAChI,KAAM;gBAAC1E,EAAE,EAAE;kBAAE4C,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBAACsC,OAAO,EAAGC,CAAC,IAAK;kBAAEA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,2CAA2C;gBAAE;cAAE;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChLpC,OAAA,CAAC3B,GAAG;gBAAA0D,QAAA,gBACF/B,OAAA,CAAC5B,UAAU;kBAACiE,OAAO,EAAC,WAAW;kBAAC1B,EAAE,EAAE;oBAAE2B,UAAU,EAAE,GAAG;oBAAEnB,KAAK,EAAE,UAAU;oBAAE6D,UAAU,EAAE;kBAAI,CAAE;kBAAAjD,QAAA,EACzFsL,IAAI,CAAChI,KAAK,CAACxC,MAAM,GAAG,EAAE,GAAGwK,IAAI,CAAChI,KAAK,CAAC0F,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGsC,IAAI,CAAChI;gBAAK;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACbpC,OAAA,CAAC5B,UAAU;kBAACiE,OAAO,EAAC,SAAS;kBAAC1B,EAAE,EAAE;oBAAEQ,KAAK,EAAE;kBAAW,CAAE;kBAAAY,QAAA,GACrD2G,UAAU,CAAC2E,IAAI,CAAC7H,UAAU,IAAI6H,IAAI,CAAC/I,IAAI,CAAC,EAAC,UAAG,EAAC+I,IAAI,CAACC,KAAK,IAAI,CAAC,EAAC,QAChE;gBAAA;kBAAArL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA,GATEiL,IAAI,CAACnH,EAAE,IAAIwG,GAAG;cAAAzK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUnB,CACN,CAAC,gBAEFpC,OAAA,CAAC5B,UAAU;cAACiE,OAAO,EAAC,OAAO;cAAC1B,EAAE,EAAE;gBAAEQ,KAAK,EAAE,UAAU;gBAAEuG,SAAS,EAAE;cAAS,CAAE;cAAA3F,QAAA,EAAC;YAE5E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACmL,GAAA,GAtLQJ,iBAAiB;AAwL1B,eAAe,SAASK,WAAWA,CAAA,EAAG;EAAAC,GAAA;EACpC,MAAMC,QAAQ,GAAG1P,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACyE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3E,QAAQ,CAAC,OAAO,CAAC;EACjE,MAAM,CAACwM,WAAW,EAAEoD,cAAc,CAAC,GAAG5P,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqM,SAAS,EAAEC,YAAY,CAAC,GAAGtM,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACsI,MAAM,EAAEuH,SAAS,CAAC,GAAG7P,QAAQ,CAAC;IAAE2N,IAAI,EAAE,EAAE;IAAErG,KAAK,EAAE;EAAG,CAAC,CAAC;EAC7D,MAAM,CAAClB,QAAQ,EAAE0J,WAAW,CAAC,GAAG9P,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiM,WAAW,EAAE8D,cAAc,CAAC,GAAG/P,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4E,cAAc,EAAEoL,iBAAiB,CAAC,GAAGhQ,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACoM,OAAO,EAAE6D,UAAU,CAAC,GAAGjQ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkM,aAAa,EAAEgE,gBAAgB,CAAC,GAAGlQ,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACmQ,eAAe,EAAEC,kBAAkB,CAAC,GAAGpQ,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqQ,YAAY,EAAEC,eAAe,CAAC,GAAGtQ,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuQ,WAAW,EAAEC,cAAc,CAAC,GAAGxQ,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2M,cAAc,EAAE8D,iBAAiB,CAAC,GAAGzQ,QAAQ,CAAC,IAAI0Q,GAAG,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG5Q,QAAQ,CAAC;IAAE6Q,OAAO,EAAE,KAAK;IAAEpO,OAAO,EAAE;EAAG,CAAC,CAAC;;EAEnE;EACA1C,KAAK,CAAC6I,SAAS,CAAC,MAAM;IACpB,MAAMkI,SAAS,GAAG,IAAIC,eAAe,CAAC/B,MAAM,CAACC,QAAQ,CAAC+B,MAAM,CAAC;IAC7D,MAAMC,QAAQ,GAAGH,SAAS,CAACI,GAAG,CAAC,UAAU,CAAC;IAC1C,MAAMC,QAAQ,GAAGL,SAAS,CAACI,GAAG,CAAC,QAAQ,CAAC;IAExC,IAAID,QAAQ,IAAIA,QAAQ,KAAK,OAAO,EAAE;MACpCtM,mBAAmB,CAACsM,QAAQ,CAAC;IAC/B;;IAEA;IACA,IAAIE,QAAQ,EAAE;MACZ;MACAC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,QAAQ,CAAC;IAC1C;EACF,CAAC,EAAE,EAAE,CAAC;EACN,MAAMG,KAAK,GAAGpQ,QAAQ,CAAC,CAAC;EACxB,MAAMqQ,SAAS,GAAGtQ,aAAa,CAACqQ,KAAK,CAACE,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,CAAC;EAC3D,MAAMC,QAAQ,GAAGzQ,aAAa,CAACqQ,KAAK,CAACE,WAAW,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;EAG5D,MAAMpF,aAAa,GAAGA,CAAA,KAAMqD,cAAc,CAAC,IAAI,CAAC;EAChD,MAAMnD,kBAAkB,GAAGA,CAAA,KAAMmD,cAAc,CAAC,KAAK,CAAC;EAEtD,MAAMlD,WAAW,GAAG,MAAO5C,IAAI,IAAK;IAClC,IAAI;MACF;MACA8H,KAAK,CAAC,kCAAkC,GAAG9H,IAAI,CAAC3B,EAAE,GAAG,QAAQ,EAAE;QAC7D0J,MAAM,EAAE;MACV,CAAC,CAAC,CAACC,KAAK,CAACC,GAAG,IAAIX,OAAO,CAACY,KAAK,CAAC,6BAA6B,EAAED,GAAG,CAAC,CAAC;;MAElE;MACA,MAAME,QAAQ,GAAGnI,IAAI,CAACxC,KAAK,CACxB4K,WAAW,CAAC,CAAC,CACbC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;MAAA,CAC7BA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;MAAA,CACrBA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;MAAA,CACpBC,IAAI,CAAC,CAAC;MAET,MAAMC,IAAI,GAAG,GAAGrD,MAAM,CAACC,QAAQ,CAACqD,MAAM,YAAYxI,IAAI,CAAC3B,EAAE,UAAU8J,QAAQ,aAAanI,IAAI,CAACtC,QAAQ,EAAE;;MAEvG;MACA,MAAM+K,SAAS,CAACC,SAAS,CAACC,SAAS,CAACJ,IAAI,CAAC;;MAEzC;MACAvC,WAAW,CAAC4C,QAAQ,IAClBA,QAAQ,CAAC3N,GAAG,CAACuK,IAAI,IACfA,IAAI,CAACnH,EAAE,KAAK2B,IAAI,CAAC3B,EAAE,GACf;QAAE,GAAGmH,IAAI;QAAEqD,KAAK,EAAE,CAACrD,IAAI,CAACqD,KAAK,IAAI,CAAC,IAAI;MAAE,CAAC,GACzCrD,IACN,CACF,CAAC;;MAED;MACAsB,QAAQ,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAEpO,OAAO,EAAE;MAA6C,CAAC,CAAC;;MAElF;MACAmQ,UAAU,CAAC,MAAM;QACfhC,QAAQ,CAAC;UAAEC,OAAO,EAAE,KAAK;UAAEpO,OAAO,EAAE;QAAG,CAAC,CAAC;MAC3C,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOuP,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CpB,QAAQ,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAEpO,OAAO,EAAE;MAA6B,CAAC,CAAC;MAClEmQ,UAAU,CAAC,MAAM;QACfhC,QAAQ,CAAC;UAAEC,OAAO,EAAE,KAAK;UAAEpO,OAAO,EAAE;QAAG,CAAC,CAAC;MAC3C,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,MAAMoQ,cAAc,GAAG,MAAO/I,IAAI,IAAK;IACrC,MAAMgJ,qBAAqB,GAAGnG,cAAc,CAACiC,GAAG,CAAC9E,IAAI,CAAC3B,EAAE,CAAC;IAEzD,IAAI;MACF;MACA,MAAM4K,MAAM,GAAGD,qBAAqB,GAAG,mBAAmB,GAAG,gBAAgB;MAE7E,MAAME,QAAQ,GAAG,MAAMpB,KAAK,CAAC,8DAA8D,EAAE;QAC3FC,MAAM,EAAE,MAAM;QACdoB,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBL,MAAM,EAAEA,MAAM;UACdM,OAAO,EAAEvJ,IAAI,CAAC3B;QAChB,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAAC6K,QAAQ,CAACM,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBP,QAAQ,CAACQ,MAAM,EAAE,CAAC;MAC3D;MAEA,MAAMC,MAAM,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;MAEpC,IAAID,MAAM,CAACE,OAAO,EAAE;QAClB;QACAlD,iBAAiB,CAAClH,IAAI,IAAI;UACxB,MAAMqK,MAAM,GAAG,IAAIlD,GAAG,CAACnH,IAAI,CAAC;UAC5B,IAAIuJ,qBAAqB,EAAE;YACzBc,MAAM,CAACC,MAAM,CAAC/J,IAAI,CAAC3B,EAAE,CAAC;UACxB,CAAC,MAAM;YACLyL,MAAM,CAACE,GAAG,CAAChK,IAAI,CAAC3B,EAAE,CAAC;UACrB;UACA,OAAOyL,MAAM;QACf,CAAC,CAAC;;QAEF;QACA,MAAMnR,OAAO,GAAGqQ,qBAAqB,GACjC,8BAA8B,GAC9B,gCAAgC;QACpClC,QAAQ,CAAC;UAAEC,OAAO,EAAE,IAAI;UAAEpO;QAAQ,CAAC,CAAC;QAEpCmQ,UAAU,CAAC,MAAM;UACfhC,QAAQ,CAAC;YAAEC,OAAO,EAAE,KAAK;YAAEpO,OAAO,EAAE;UAAG,CAAC,CAAC;QAC3C,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLmO,QAAQ,CAAC;UAAEC,OAAO,EAAE,IAAI;UAAEpO,OAAO,EAAE,6BAA6B,IAAIgR,MAAM,CAAChR,OAAO,IAAI,eAAe;QAAE,CAAC,CAAC;QACzGmQ,UAAU,CAAC,MAAM;UACfhC,QAAQ,CAAC;YAAEC,OAAO,EAAE,KAAK;YAAEpO,OAAO,EAAE;UAAG,CAAC,CAAC;QAC3C,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC,OAAOuP,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDpB,QAAQ,CAAC;QAAEC,OAAO,EAAE,IAAI;QAAEpO,OAAO,EAAE,SAAS,GAAGuP,KAAK,CAACvP;MAAQ,CAAC,CAAC;MAC/DmQ,UAAU,CAAC,MAAM;QACfhC,QAAQ,CAAC;UAAEC,OAAO,EAAE,KAAK;UAAEpO,OAAO,EAAE;QAAG,CAAC,CAAC;MAC3C,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;;EAED;EACA,MAAMsR,UAAU,GAAGA,CAAA,KAAM;IACvBzD,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EACD;EACA,MAAMwD,WAAW,GAAGA,CAAA,KAAM;IACxB1D,eAAe,CAAC,KAAK,CAAC;IACtBE,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EACD;EACA,MAAMyD,cAAc,GAAG1D,WAAW,CAAC6B,IAAI,CAAC,CAAC,GAAGhM,QAAQ,CAACkH,MAAM,CACzD4G,CAAC,IACCA,CAAC,CAAC5M,KAAK,CAAC4K,WAAW,CAAC,CAAC,CAACiC,QAAQ,CAAC5D,WAAW,CAAC2B,WAAW,CAAC,CAAC,CAAC,IACzDgC,CAAC,CAACtI,WAAW,CAACsG,WAAW,CAAC,CAAC,CAACiC,QAAQ,CAAC5D,WAAW,CAAC2B,WAAW,CAAC,CAAC,CAClE,CAAC,CAACrL,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE;EAClB;EACA9G,KAAK,CAAC6I,SAAS,CAAC,MAAM;IACpB,IAAI,CAACyH,YAAY,EAAE;IACnB,MAAM+D,SAAS,GAAInM,CAAC,IAAK;MACvB,IAAIA,CAAC,CAACoM,GAAG,KAAK,QAAQ,EAAEL,WAAW,CAAC,CAAC;IACvC,CAAC;IACDhF,MAAM,CAACsF,gBAAgB,CAAC,SAAS,EAAEF,SAAS,CAAC;IAC7C,OAAO,MAAMpF,MAAM,CAACuF,mBAAmB,CAAC,SAAS,EAAEH,SAAS,CAAC;EAC/D,CAAC,EAAE,CAAC/D,YAAY,CAAC,CAAC;;EAElB;EACAtQ,KAAK,CAAC6I,SAAS,CAAC,MAAM;IACpB;IACAgJ,KAAK,CAAC,kFAAkF,CAAC,CACtF4C,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACf,IAAI,CAAC,CAAC,CAAC,CACvBc,IAAI,CAACE,IAAI,IAAI;MACZ;MACA,IAAIC,QAAQ,GAAGvS,WAAW,CAACsS,IAAI,CAACE,YAAY,CAAC,IAAI,cAAc;MAE/D/E,SAAS,CAAC;QACRlC,IAAI,EAAEgH,QAAQ;QACdrN,KAAK,EAAEoN,IAAI,CAACG,YAAY,IAAI,mBAAmB;QAC/CjJ,WAAW,EAAE8I,IAAI,CAACI,mBAAmB,IAAI,sCAAsC;QAC/EC,aAAa,EAAEL,IAAI,CAACK,aAAa,IAAI,SAAS;QAC9CC,eAAe,EAAEN,IAAI,CAACM,eAAe,IAAI,SAAS;QAClDC,YAAY,EAAEP,IAAI,CAACO,YAAY,IAAI,SAAS;QAC5CC,WAAW,EAAER,IAAI,CAACQ,WAAW,IAAI,gDAAgD;QACjFC,aAAa,EAAET,IAAI,CAACS,aAAa,IAAI,qBAAqB;QAC1DC,eAAe,EAAEV,IAAI,CAACU,eAAe,IAAI,EAAE;QAC3CC,cAAc,EAAEX,IAAI,CAACW,cAAc,IAAI,EAAE;QACzCC,gBAAgB,EAAEZ,IAAI,CAACY,gBAAgB,IAAI,EAAE;QAC7CC,cAAc,EAAEb,IAAI,CAACa,cAAc,IAAI,EAAE;QACzCC,aAAa,EAAEd,IAAI,CAACc,aAAa,IAAI,sBAAsB;QAC3DC,gBAAgB,EAAEf,IAAI,CAACe,gBAAgB,IAAI;MAC7C,CAAC,CAAC;;MAEF;MACA3M,QAAQ,CAACxB,KAAK,GAAGoN,IAAI,CAACG,YAAY,IAAI,mBAAmB;;MAEzD;MACA,MAAMa,eAAe,GAAG5M,QAAQ,CAAC6M,aAAa,CAAC,0BAA0B,CAAC;MAC1E,IAAID,eAAe,EAAE;QACnBA,eAAe,CAACE,YAAY,CAAC,SAAS,EAAElB,IAAI,CAACe,gBAAgB,IAAI,uBAAuB,CAAC;MAC3F;;MAEA;MACA,MAAMI,YAAY,GAAG/M,QAAQ,CAAC6M,aAAa,CAAC,uBAAuB,CAAC;MACpE,IAAIE,YAAY,EAAE;QAChBA,YAAY,CAACD,YAAY,CAAC,SAAS,EAAElB,IAAI,CAACc,aAAa,IAAI,sBAAsB,CAAC;MACpF;IACF,CAAC,CAAC,CACD1D,KAAK,CAACC,GAAG,IAAI;MACZX,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEU,GAAG,CAAC;MACpE;MACAlC,SAAS,CAAC;QACRlC,IAAI,EAAE,cAAc;QACpBrG,KAAK,EAAE,mBAAmB;QAC1BsE,WAAW,EAAE,sCAAsC;QACnDmJ,aAAa,EAAE,SAAS;QACxBC,eAAe,EAAE,SAAS;QAC1BC,YAAY,EAAE,SAAS;QACvBC,WAAW,EAAE,gDAAgD;QAC7DC,aAAa,EAAE,qBAAqB;QACpCC,eAAe,EAAE,EAAE;QACnBC,cAAc,EAAE,EAAE;QAClBC,gBAAgB,EAAE,EAAE;QACpBC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE,6BAA6B;QAC5CC,gBAAgB,EAAE;MACpB,CAAC,CAAC;;MAEF;MACA3M,QAAQ,CAACxB,KAAK,GAAG,mBAAmB;IACtC,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMwO,eAAe,GAAG/V,KAAK,CAACgW,WAAW,CAAC,MAAM;IAC9CnE,KAAK,CAAC,qFAAqF,GAAGpL,IAAI,CAACwP,GAAG,CAAC,CAAC,CAAC,CACtGxB,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACf,IAAI,CAAC,CAAC,CAAC,CACvBc,IAAI,CAACE,IAAI,IAAI;MACZ;MACA,IAAIC,QAAQ,GAAGvS,WAAW,CAACsS,IAAI,CAACE,YAAY,CAAC,IAAI,cAAc;MAE/D/E,SAAS,CAACtG,IAAI,KAAK;QACjB,GAAGA,IAAI;QACPoE,IAAI,EAAEgH,QAAQ;QACdrN,KAAK,EAAEoN,IAAI,CAACG,YAAY,IAAI,mBAAmB;QAC/CjJ,WAAW,EAAE8I,IAAI,CAACI,mBAAmB,IAAI;MAC3C,CAAC,CAAC,CAAC;MAEHhM,QAAQ,CAACxB,KAAK,GAAGoN,IAAI,CAACG,YAAY,IAAI,mBAAmB;IAC3D,CAAC,CAAC,CACD/C,KAAK,CAACC,GAAG,IAAIX,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEU,GAAG,CAAC,CAAC;EAClE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAhS,KAAK,CAAC6I,SAAS,CAAC,MAAM;IACpB,MAAMqN,mBAAmB,GAAIhO,CAAC,IAAK;MACjC,IAAIA,CAAC,CAACoM,GAAG,KAAK,kBAAkB,EAAE;QAChCjD,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9CyE,eAAe,CAAC,CAAC;MACnB;IACF,CAAC;IAED9G,MAAM,CAACsF,gBAAgB,CAAC,SAAS,EAAE2B,mBAAmB,CAAC;IACvD,OAAO,MAAMjH,MAAM,CAACuF,mBAAmB,CAAC,SAAS,EAAE0B,mBAAmB,CAAC;EACzE,CAAC,EAAE,CAACH,eAAe,CAAC,CAAC;;EAErB;EACA/V,KAAK,CAAC6I,SAAS,CAAC,MAAM;IACpBgJ,KAAK,CAAC,oFAAoF,CAAC,CACxF4C,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACf,IAAI,CAAC,CAAC,CAAC,CACvBc,IAAI,CAACE,IAAI,IAAI;MACZ,IAAIA,IAAI,CAACf,OAAO,IAAIlF,KAAK,CAACyH,OAAO,CAACxB,IAAI,CAACA,IAAI,CAAC,EAAE;QAC5C;QACA,MAAMyB,gBAAgB,GAAGzB,IAAI,CAACA,IAAI,CAAC3P,GAAG,CAACC,GAAG,KAAK;UAC7CmD,EAAE,EAAEnD,GAAG,CAACmD,EAAE;UACVlD,IAAI,EAAED,GAAG,CAACC,IAAI;UACdmR,IAAI,EAAEpR,GAAG,CAACoR,IAAI;UACdxK,WAAW,EAAE5G,GAAG,CAAC4G,WAAW;UAC5BxI,KAAK,EAAE4B,GAAG,CAAC5B,KAAK,IAAI,SAAS;UAC7BiT,SAAS,EAAErR,GAAG,CAACqR,SAAS;UACxBlR,UAAU,EAAEH,GAAG,CAACG,UAAU,IAAI,CAAC;UAC/BsC,UAAU,EAAEzC,GAAG,CAACyC,UAAU;UAC1B6O,UAAU,EAAEtR,GAAG,CAACsR;QAClB,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMC,gBAAgB,GAAGJ,gBAAgB,CAAC7I,MAAM,CAACtI,GAAG,IAAIA,GAAG,CAACqR,SAAS,KAAK,KAAK,CAAC;QAChFrG,iBAAiB,CAACuG,gBAAgB,CAAC;MACrC;IACF,CAAC,CAAC,CACDzE,KAAK,CAACC,GAAG,IAAI;MACZX,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvE;MACArB,iBAAiB,CAAC,CAChB;QACE7H,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,MAAM;QACZmR,IAAI,EAAE,MAAM;QACZxK,WAAW,EAAE,mCAAmC;QAChDxI,KAAK,EAAE,SAAS;QAChBiT,SAAS,EAAE,IAAI;QACflR,UAAU,EAAE;MACd,CAAC,EACD;QACEgD,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,WAAW;QACjBmR,IAAI,EAAE,WAAW;QACjBxK,WAAW,EAAE,sCAAsC;QACnDxI,KAAK,EAAE,SAAS;QAChBiT,SAAS,EAAE,IAAI;QACflR,UAAU,EAAE;MACd,CAAC,EACD;QACEgD,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,QAAQ;QACdmR,IAAI,EAAE,QAAQ;QACdxK,WAAW,EAAE,2BAA2B;QACxCxI,KAAK,EAAE,SAAS;QAChBiT,SAAS,EAAE,IAAI;QACflR,UAAU,EAAE;MACd,CAAC,EACD;QACEgD,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,UAAU;QAChBmR,IAAI,EAAE,UAAU;QAChBxK,WAAW,EAAE,+BAA+B;QAC5CxI,KAAK,EAAE,SAAS;QAChBiT,SAAS,EAAE,IAAI;QACflR,UAAU,EAAE;MACd,CAAC,EACD;QACEgD,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,SAAS;QACfmR,IAAI,EAAE,SAAS;QACfxK,WAAW,EAAE,8BAA8B;QAC3CxI,KAAK,EAAE,SAAS;QAChBiT,SAAS,EAAE,IAAI;QACflR,UAAU,EAAE;MACd,CAAC,EACD;QACEgD,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,SAAS;QACfmR,IAAI,EAAE,SAAS;QACfxK,WAAW,EAAE,iCAAiC;QAC9CxI,KAAK,EAAE,SAAS;QAChBiT,SAAS,EAAE,IAAI;QACflR,UAAU,EAAE;MACd,CAAC,EACD;QACEgD,EAAE,EAAE,CAAC;QACLlD,IAAI,EAAE,WAAW;QACjBmR,IAAI,EAAE,WAAW;QACjBxK,WAAW,EAAE,4BAA4B;QACzCxI,KAAK,EAAE,SAAS;QAChBiT,SAAS,EAAE,IAAI;QACflR,UAAU,EAAE;MACd,CAAC,CACF,CAAC;IACJ,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApF,KAAK,CAAC6I,SAAS,CAAC,MAAM;IACpBgJ,KAAK,CAAC,8EAA8E,CAAC,CAClF4C,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACf,IAAI,CAAC,CAAC,CAAC,CACvBc,IAAI,CAACE,IAAI,IAAI;MACZ,IAAIA,IAAI,CAACf,OAAO,IAAIlF,KAAK,CAACyH,OAAO,CAACxB,IAAI,CAACA,IAAI,CAAC,EAAE;QAC5C;QACA,MAAM8B,UAAU,GAAG9B,IAAI,CAACA,IAAI,CAAC3P,GAAG,CAACuK,IAAI;UAAA,IAAAmH,aAAA;UAAA,OAAK;YACxCtO,EAAE,EAAEmH,IAAI,CAACnH,EAAE;YACXb,KAAK,EAAEgI,IAAI,CAAChI,KAAK;YACjB8O,IAAI,EAAE9G,IAAI,CAAC8G,IAAI;YACfxK,WAAW,EAAE0D,IAAI,CAAC1D,WAAW,IAAI0D,IAAI,CAACoH,OAAO,IAAI,EAAAD,aAAA,GAAAnH,IAAI,CAACqH,OAAO,cAAAF,aAAA,uBAAZA,aAAA,CAAczJ,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,IAAG,KAAK;YACxF2J,OAAO,EAAErH,IAAI,CAACqH,OAAO;YACrB9O,KAAK,EAAEyH,IAAI,CAACzH,KAAK,IAAI,2CAA2C;YAChE+O,SAAS,EAAEtH,IAAI,CAACsH,SAAS,IAAItH,IAAI,CAAChI,KAAK;YACvCE,QAAQ,EAAE8H,IAAI,CAAC/H,aAAa,IAAI,MAAM;YACtCA,aAAa,EAAE+H,IAAI,CAAC/H,aAAa,IAAI,MAAM;YAC3CsP,WAAW,EAAEvH,IAAI,CAACuH,WAAW,IAAI,CAAC;YAClCC,cAAc,EAAExH,IAAI,CAACwH,cAAc,IAAI,SAAS;YAChDtD,MAAM,EAAElE,IAAI,CAACkE,MAAM,IAAI,WAAW;YAClCuD,QAAQ,EAAEzH,IAAI,CAACyH,QAAQ,IAAI,KAAK;YAChCC,IAAI,EAAE1H,IAAI,CAAC0H,IAAI,GAAG1H,IAAI,CAAC0H,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;YAC3C1H,KAAK,EAAED,IAAI,CAACC,KAAK,IAAI,CAAC;YACtBoD,KAAK,EAAErD,IAAI,CAACqD,KAAK,IAAI,CAAC;YACtBuE,KAAK,EAAE5H,IAAI,CAAC4H,KAAK,IAAI,CAAC;YACtBC,cAAc,EAAE7H,IAAI,CAAC6H,cAAc,IAAI,CAAC;YACxCC,YAAY,EAAE9H,IAAI,CAAC8H,YAAY,IAAI,CAAC;YACpC7Q,IAAI,EAAE+I,IAAI,CAAC/I,IAAI,IAAI+I,IAAI,CAAC7H,UAAU;YAClC4P,YAAY,EAAE/H,IAAI,CAAC+H,YAAY;YAC/B5P,UAAU,EAAE6H,IAAI,CAAC7H,UAAU;YAC3B6O,UAAU,EAAEhH,IAAI,CAACgH,UAAU;YAC3BgB,MAAM,EAAEhI,IAAI,CAACiI,WAAW,IAAIjI,IAAI,CAACkI,SAAS,IAAI,OAAO;YACrDC,OAAO,EAAEnI,IAAI,CAACmI;UAChB,CAAC;QAAA,CAAC,CAAC;;QAEH;QACA,MAAMC,cAAc,GAAGlB,UAAU,CAAClJ,MAAM,CAACgC,IAAI,IAAIA,IAAI,CAACkE,MAAM,KAAK,WAAW,CAAC;QAC7E1D,WAAW,CAAC4H,cAAc,CAAC;;QAE3B;QACA,MAAMC,OAAO,GAAGD,cAAc,CAC3BE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UACd,MAAMC,MAAM,GAAIF,CAAC,CAACtI,KAAK,GAAG,CAAC,GAAKsI,CAAC,CAAClF,KAAK,GAAG,CAAE,GAAIkF,CAAC,CAACX,KAAK,GAAG,CAAE,GAAIW,CAAC,CAACV,cAAc,GAAG,CAAE;UACrF,MAAMa,MAAM,GAAIF,CAAC,CAACvI,KAAK,GAAG,CAAC,GAAKuI,CAAC,CAACnF,KAAK,GAAG,CAAE,GAAImF,CAAC,CAACZ,KAAK,GAAG,CAAE,GAAIY,CAAC,CAACX,cAAc,GAAG,CAAE;UACrF,OAAOa,MAAM,GAAGD,MAAM;QACxB,CAAC,CAAC,CACDlR,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACdkJ,cAAc,CAAC4H,OAAO,CAAC;MACzB,CAAC,MAAM;QACL7H,WAAW,CAAC,EAAE,CAAC;QACfC,cAAc,CAAC,EAAE,CAAC;MACpB;MACAE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACD6B,KAAK,CAAC,MAAM;MACXV,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvE;MACA,MAAM4G,SAAS,GAAG,CAChB;QACE9P,EAAE,EAAE,CAAC;QACLb,KAAK,EAAE,qCAAqC;QAC5C8O,IAAI,EAAE,qCAAqC;QAC3CxK,WAAW,EAAE,8GAA8G;QAC3H+K,OAAO,EAAE,+JAA+J;QACxK9O,KAAK,EAAE,sDAAsD;QAC7D+O,SAAS,EAAE,mBAAmB;QAC9BpP,QAAQ,EAAE,WAAW;QACrBD,aAAa,EAAE,WAAW;QAC1BsP,WAAW,EAAE,CAAC;QACdC,cAAc,EAAE,SAAS;QACzBtD,MAAM,EAAE,WAAW;QACnBuD,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC;QAChDzH,KAAK,EAAE,GAAG;QACVoD,KAAK,EAAE,EAAE;QACTuE,KAAK,EAAE,EAAE;QACTC,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE,CAAC;QACf7Q,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAAC0R,WAAW,CAAC,CAAC;QAC9Bb,YAAY,EAAE,IAAI7Q,IAAI,CAAC,CAAC,CAAC0R,WAAW,CAAC,CAAC;QACtCzQ,UAAU,EAAE,IAAIjB,IAAI,CAAC,CAAC,CAAC0R,WAAW,CAAC,CAAC;QACpC5B,UAAU,EAAE,IAAI9P,IAAI,CAAC,CAAC,CAAC0R,WAAW,CAAC,CAAC;QACpCZ,MAAM,EAAE,OAAO;QACfG,OAAO,EAAE;MACX,CAAC,EACD;QACEtP,EAAE,EAAE,CAAC;QACLb,KAAK,EAAE,6CAA6C;QACpD8O,IAAI,EAAE,6CAA6C;QACnDxK,WAAW,EAAE,qGAAqG;QAClH+K,OAAO,EAAE,gKAAgK;QACzK9O,KAAK,EAAE,oDAAoD;QAC3D+O,SAAS,EAAE,cAAc;QACzBpP,QAAQ,EAAE,WAAW;QACrBD,aAAa,EAAE,WAAW;QAC1BsP,WAAW,EAAE,CAAC;QACdC,cAAc,EAAE,SAAS;QACzBtD,MAAM,EAAE,WAAW;QACnBuD,QAAQ,EAAE,KAAK;QACfC,IAAI,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,CAAC;QAC/CzH,KAAK,EAAE,EAAE;QACToD,KAAK,EAAE,EAAE;QACTuE,KAAK,EAAE,EAAE;QACTC,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE,CAAC;QACf7Q,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACwP,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAACkC,WAAW,CAAC,CAAC;QACnDb,YAAY,EAAE,IAAI7Q,IAAI,CAACA,IAAI,CAACwP,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAACkC,WAAW,CAAC,CAAC;QAC3DzQ,UAAU,EAAE,IAAIjB,IAAI,CAACA,IAAI,CAACwP,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAACkC,WAAW,CAAC,CAAC;QACzD5B,UAAU,EAAE,IAAI9P,IAAI,CAACA,IAAI,CAACwP,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAACkC,WAAW,CAAC,CAAC;QACzDZ,MAAM,EAAE,OAAO;QACfG,OAAO,EAAE;MACX,CAAC,EACD;QACEtP,EAAE,EAAE,CAAC;QACLb,KAAK,EAAE,4CAA4C;QACnD8O,IAAI,EAAE,4CAA4C;QAClDxK,WAAW,EAAE,qFAAqF;QAClG+K,OAAO,EAAE,0IAA0I;QACnJ9O,KAAK,EAAE,0DAA0D;QACjE+O,SAAS,EAAE,mBAAmB;QAC9BpP,QAAQ,EAAE,QAAQ;QAClBD,aAAa,EAAE,QAAQ;QACvBsP,WAAW,EAAE,CAAC;QACdC,cAAc,EAAE,SAAS;QACzBtD,MAAM,EAAE,WAAW;QACnBuD,QAAQ,EAAE,KAAK;QACfC,IAAI,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC;QACpDzH,KAAK,EAAE,EAAE;QACToD,KAAK,EAAE,CAAC;QACRuE,KAAK,EAAE,EAAE;QACTC,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE,CAAC;QACf7Q,IAAI,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACwP,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAACkC,WAAW,CAAC,CAAC;QACpDb,YAAY,EAAE,IAAI7Q,IAAI,CAACA,IAAI,CAACwP,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAACkC,WAAW,CAAC,CAAC;QAC5DzQ,UAAU,EAAE,IAAIjB,IAAI,CAACA,IAAI,CAACwP,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAACkC,WAAW,CAAC,CAAC;QAC1D5B,UAAU,EAAE,IAAI9P,IAAI,CAACA,IAAI,CAACwP,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAACkC,WAAW,CAAC,CAAC;QAC1DZ,MAAM,EAAE,OAAO;QACfG,OAAO,EAAE;MACX,CAAC,CACF;MACD3H,WAAW,CAACmI,SAAS,CAAC;MACtBlI,cAAc,CAACkI,SAAS,CAAC;MACzBhI,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlQ,KAAK,CAAC6I,SAAS,CAAC,MAAM;IACpB,MAAMuP,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QACF,MAAMnF,QAAQ,GAAG,MAAMpB,KAAK,CAAC,oFAAoF,CAAC;QAClH,MAAM8C,IAAI,GAAG,MAAM1B,QAAQ,CAACU,IAAI,CAAC,CAAC;QAElC,IAAIgB,IAAI,CAACf,OAAO,IAAIlF,KAAK,CAACyH,OAAO,CAACxB,IAAI,CAACA,IAAI,CAAC,EAAE;UAC5C;UACA,MAAM0D,aAAa,GAAG,IAAI1H,GAAG,CAACgE,IAAI,CAACA,IAAI,CAAC3P,GAAG,CAAC+E,IAAI,IAAIA,IAAI,CAAC3B,EAAE,CAAC,CAAC;UAC7DsI,iBAAiB,CAAC2H,aAAa,CAAC;QAClC;MACF,CAAC,CAAC,OAAOpG,KAAK,EAAE;QACdZ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEW,KAAK,CAAC;MACvD;IACF,CAAC;IAEDmG,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMhM,gBAAgB,GAAIkM,MAAM,IAAK;IACnCnI,gBAAgB,CAAC3G,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAAC8O,MAAM,GAAG,CAAC9O,IAAI,CAAC8O,MAAM;IAAE,CAAC,CAAC,CAAC;EAClE,CAAC;EAED,MAAMnN,eAAe,GAAG,MAAOoN,MAAM,IAAK;IACxC;IACA,IAAI;MACF,MAAM1G,KAAK,CAAC,0FAA0F0G,MAAM,EAAE,EAAE;QAC9GzG,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEW,KAAK,CAAC;IAClD;;IAEA;IACArC,QAAQ,CAAC,cAAc2I,MAAM,EAAE,CAAC;EAClC,CAAC;;EAED;EACA,MAAMzL,qBAAqB,GAAI0L,QAAQ,IAAK;IAC1C,IAAIA,QAAQ,KAAK,CAAC,EAAE;MAClBjM,YAAY,CAAC,CAAC,CAAC;MACf;IACF,CAAC,MAAM,IAAIiM,QAAQ,KAAK,CAAC,EAAE;MACzBjM,YAAY,CAAC,CAAC,CAAC;MACfyH,UAAU,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,MAAM,IAAIwE,QAAQ,KAAK,CAAC,EAAE;MACzBvJ,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ,CAAC,CAAC;IACnC,CAAC,MAAM;MACL5C,YAAY,CAACiM,QAAQ,CAAC;IACxB;EACF,CAAC;EAED,IAAIhH,SAAS,EAAE;IACb,oBACEtP,OAAA,CAAAE,SAAA;MAAA6B,QAAA,gBACE/B,OAAA,CAACmN,iBAAiB;QAChB9G,MAAM,EAAEA,MAAO;QACflC,QAAQ,EAAEA,QAAS;QACnB6F,WAAW,EAAEA,WAAY;QACzBrH,cAAc,EAAEA,cAAe;QAC/BF,gBAAgB,EAAEA,gBAAiB;QACnCC,mBAAmB,EAAEA,mBAAoB;QACzCuH,aAAa,EAAEA,aAAc;QAC7BC,gBAAgB,EAAEA,gBAAiB;QACnCC,OAAO,EAAEA,OAAQ;QACjBM,WAAW,EAAEA,WAAY;QACzB5B,UAAU,EAAE+H,cAAe;QAC3BlG,cAAc,EAAEA,cAAe;QAC/BC,aAAa,EAAEmH,UAAW;QAC1BvH,WAAW,EAAEA,WAAY;QACzBD,aAAa,EAAEA,aAAc;QAC7BE,kBAAkB,EAAEA,kBAAmB;QACvCzB,WAAW,EAAEE;MAAgB;QAAAhH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eAGFpC,OAAA,CAACO,KAAK;QACJC,OAAO,EAAEkO,KAAK,CAAClO,OAAQ;QACvBC,SAAS,EAAEiO,KAAK,CAACE,OAAQ;QACzBlO,OAAO,EAAEA,CAAA,KAAMiO,QAAQ,CAAC;UAAEC,OAAO,EAAE,KAAK;UAAEpO,OAAO,EAAE;QAAG,CAAC;MAAE;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,EAEDgM,YAAY,iBACXpO,OAAA,CAAC3B,GAAG;QAACsC,EAAE,EAAE;UACPC,QAAQ,EAAE,OAAO;UACjBC,GAAG,EAAE,CAAC;UACN8G,IAAI,EAAE,CAAC;UACP3G,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,IAAI;UACZC,OAAO,EAAE,wBAAwB;UACjCK,SAAS,EAAE,CAAC;UACZH,EAAE,EAAE,CAAC;UACLsI,EAAE,EAAE,CAAC;UACLD,EAAE,EAAE;QACN,CAAE;QAAA1H,QAAA,gBACA/B,OAAA,CAAC3B,GAAG;UAACsC,EAAE,EAAE;YAAEkB,QAAQ,EAAE,GAAG;YAAEuH,EAAE,EAAE,MAAM;YAAExI,QAAQ,EAAE,UAAU;YAAE0I,EAAE,EAAE;UAAE,CAAE;UAAAvH,QAAA,gBAClE/B,OAAA,CAACN,SAAS;YACR6W,SAAS;YACTvK,SAAS;YACTwK,KAAK,EAAElI,WAAY;YACnBmI,QAAQ,EAAEzQ,CAAC,IAAIuI,cAAc,CAACvI,CAAC,CAACC,MAAM,CAACuQ,KAAK,CAAE;YAC9CE,WAAW,EAAC,wBAAwB;YACpCrU,OAAO,EAAC,UAAU;YAClB1B,EAAE,EAAE;cAAEqB,QAAQ,EAAE,EAAE;cAAEd,OAAO,EAAE;YAAO;UAAE;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACFpC,OAAA,CAACV,UAAU;YACTyE,OAAO,EAAEgO,WAAY;YACrBpR,EAAE,EAAE;cAAEC,QAAQ,EAAE,UAAU;cAAEC,GAAG,EAAE,CAAC;cAAEG,KAAK,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAG,CAAE;YAC3D,cAAW,OAAO;YAAAc,QAAA,eAElB/B,OAAA,CAACR,SAAS;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAELkM,WAAW,CAAC6B,IAAI,CAAC,CAAC,iBACjBnQ,OAAA,CAAC3B,GAAG;UAACsC,EAAE,EAAE;YAAEkB,QAAQ,EAAE,GAAG;YAAEuH,EAAE,EAAE,MAAM;YAAEE,EAAE,EAAE,CAAC;YAAEpI,OAAO,EAAE,MAAM;YAAEI,YAAY,EAAE,CAAC;YAAEC,SAAS,EAAE,CAAC;YAAE4B,CAAC,EAAE;UAAE,CAAE;UAAApB,QAAA,EACjGiQ,cAAc,CAACnP,MAAM,KAAK,CAAC,gBAC1B7C,OAAA,CAAC5B,UAAU;YAACiE,OAAO,EAAC,OAAO;YAAC1B,EAAE,EAAE;cAAEQ,KAAK,EAAE,UAAU;cAAEuG,SAAS,EAAE,QAAQ;cAAErG,EAAE,EAAE;YAAE,CAAE;YAAAU,QAAA,EAAC;UAEnF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,GAEb4P,cAAc,CAAClP,GAAG,CAAEuK,IAAI,iBACtBrN,OAAA,CAAC3B,GAAG;YAAesC,EAAE,EAAE;cAAEU,EAAE,EAAE,CAAC;cAAED,EAAE,EAAE,CAAC;cAAEoK,YAAY,EAAE,gBAAgB;cAAExH,MAAM,EAAE,SAAS;cAAE,cAAc,EAAE;gBAAEwH,YAAY,EAAE;cAAE;YAAE,CAAE;YAAAzJ,QAAA,gBAC9H/B,OAAA,CAAC5B,UAAU;cAACiE,OAAO,EAAC,WAAW;cAAC1B,EAAE,EAAE;gBAAE2B,UAAU,EAAE,GAAG;gBAAEnB,KAAK,EAAE;cAAe,CAAE;cAAAY,QAAA,EAAEsL,IAAI,CAAChI;YAAK;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACzGpC,OAAA,CAAC5B,UAAU;cAACiE,OAAO,EAAC,SAAS;cAAC1B,EAAE,EAAE;gBAAEQ,KAAK,EAAE;cAAW,CAAE;cAAAY,QAAA,GAAEsL,IAAI,CAAC9H,QAAQ,EAAC,UAAQ,EAAC,IAAIhB,IAAI,CAAC8I,IAAI,CAAC/I,IAAI,CAAC,CAAC0G,kBAAkB,CAAC,OAAO,EAAE;gBAAEC,GAAG,EAAE,SAAS;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,IAAI,EAAE;cAAU,CAAC,CAAC;YAAA;cAAAlJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAClMpC,OAAA,CAAC5B,UAAU;cAACiE,OAAO,EAAC,OAAO;cAAC1B,EAAE,EAAE;gBAAEQ,KAAK,EAAE;cAAW,CAAE;cAAAY,QAAA,EACnDsL,IAAI,CAAC1D,WAAW,CAAC9G,MAAM,GAAG,EAAE,GAAGwK,IAAI,CAAC1D,WAAW,CAACoB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGsC,IAAI,CAAC1D;YAAW;cAAA1H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC;UAAA,GALLiL,IAAI,CAACnH,EAAE;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMZ,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA,eACD,CAAC;EAEP;EACA,oBACEpC,OAAA,CAAAE,SAAA;IAAA6B,QAAA,gBACE/B,OAAA,CAAC+J,gBAAgB;MACf1D,MAAM,EAAEA,MAAO;MACflC,QAAQ,EAAEA,QAAS;MACnB6F,WAAW,EAAEA,WAAY;MACzBrH,cAAc,EAAEA,cAAe;MAC/BF,gBAAgB,EAAEA,gBAAiB;MACnCC,mBAAmB,EAAEA,mBAAoB;MACzCuH,aAAa,EAAEA,aAAc;MAC7BC,gBAAgB,EAAEA,gBAAiB;MACnCC,OAAO,EAAEA,OAAQ;MACjBC,SAAS,EAAEA,SAAU;MACrBC,YAAY,EAAEA,YAAa;MAC3BC,aAAa,EAAEA,aAAc;MAC7BC,WAAW,EAAEA,WAAY;MACzBC,kBAAkB,EAAEA,kBAAmB;MACvCC,WAAW,EAAEA,WAAY;MACzB5B,UAAU,EAAE+H,cAAe;MAC3BlG,cAAc,EAAEA,cAAe;MAC/BC,aAAa,EAAEmH,UAAW;MAC1BlH,qBAAqB,EAAEA,qBAAsB;MAC7C7B,WAAW,EAAEE;IAAgB;MAAAhH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eAGFpC,OAAA,CAACO,KAAK;MACJC,OAAO,EAAEkO,KAAK,CAAClO,OAAQ;MACvBC,SAAS,EAAEiO,KAAK,CAACE,OAAQ;MACzBlO,OAAO,EAAEA,CAAA,KAAMiO,QAAQ,CAAC;QAAEC,OAAO,EAAE,KAAK;QAAEpO,OAAO,EAAE;MAAG,CAAC;IAAE;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,EAEDgM,YAAY,iBACXpO,OAAA,CAAC3B,GAAG;MAACsC,EAAE,EAAE;QACPC,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,CAAC;QACN8G,IAAI,EAAE,CAAC;QACP3G,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,wBAAwB;QACjCK,SAAS,EAAE,CAAC;QACZH,EAAE,EAAE;UAAEN,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QACpB2I,EAAE,EAAE;UAAE5I,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QAAE;QACtB0I,EAAE,EAAE;MACN,CAAE;MAAA1H,QAAA,gBACA/B,OAAA,CAAC3B,GAAG;QAACsC,EAAE,EAAE;UAAEkB,QAAQ,EAAE,GAAG;UAAEuH,EAAE,EAAE,MAAM;UAAExI,QAAQ,EAAE;QAAW,CAAE;QAAAmB,QAAA,gBAC3D/B,OAAA,CAACN,SAAS;UACR6W,SAAS;UACTvK,SAAS;UACTwK,KAAK,EAAElI,WAAY;UACnBmI,QAAQ,EAAEzQ,CAAC,IAAIuI,cAAc,CAACvI,CAAC,CAACC,MAAM,CAACuQ,KAAK,CAAE;UAC9CE,WAAW,EAAC,wBAAwB;UACpCrU,OAAO,EAAC,UAAU;UAClB1B,EAAE,EAAE;YAAEqB,QAAQ,EAAE,EAAE;YAAEd,OAAO,EAAE;UAAO;QAAE;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACFpC,OAAA,CAACV,UAAU;UACTyE,OAAO,EAAEgO,WAAY;UACrBpR,EAAE,EAAE;YAAEC,QAAQ,EAAE,UAAU;YAAEC,GAAG,EAAE,CAAC;YAAEG,KAAK,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAG,CAAE;UAC3D,cAAW,OAAO;UAAAc,QAAA,eAElB/B,OAAA,CAACR,SAAS;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EAELkM,WAAW,CAAC6B,IAAI,CAAC,CAAC,iBACjBnQ,OAAA,CAAC3B,GAAG;QAACsC,EAAE,EAAE;UAAEkB,QAAQ,EAAE,GAAG;UAAEuH,EAAE,EAAE,MAAM;UAAEE,EAAE,EAAE,CAAC;UAAEpI,OAAO,EAAE,MAAM;UAAEI,YAAY,EAAE,CAAC;UAAEC,SAAS,EAAE,CAAC;UAAE4B,CAAC,EAAE;QAAE,CAAE;QAAApB,QAAA,EACjGiQ,cAAc,CAACnP,MAAM,KAAK,CAAC,gBAC1B7C,OAAA,CAAC5B,UAAU;UAACiE,OAAO,EAAC,OAAO;UAAC1B,EAAE,EAAE;YAAEQ,KAAK,EAAE,UAAU;YAAEuG,SAAS,EAAE,QAAQ;YAAErG,EAAE,EAAE;UAAE,CAAE;UAAAU,QAAA,EAAC;QAEnF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,GAEb4P,cAAc,CAAClP,GAAG,CAAEuK,IAAI,iBACtBrN,OAAA,CAAC3B,GAAG;UAAesC,EAAE,EAAE;YAAEU,EAAE,EAAE,CAAC;YAAED,EAAE,EAAE,CAAC;YAAEoK,YAAY,EAAE,gBAAgB;YAAExH,MAAM,EAAE,SAAS;YAAE,cAAc,EAAE;cAAEwH,YAAY,EAAE;YAAE;UAAE,CAAE;UAAAzJ,QAAA,gBAC9H/B,OAAA,CAAC5B,UAAU;YAACiE,OAAO,EAAC,WAAW;YAAC1B,EAAE,EAAE;cAAE2B,UAAU,EAAE,GAAG;cAAEnB,KAAK,EAAE;YAAe,CAAE;YAAAY,QAAA,EAAEsL,IAAI,CAAChI;UAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzGpC,OAAA,CAAC5B,UAAU;YAACiE,OAAO,EAAC,SAAS;YAAC1B,EAAE,EAAE;cAAEQ,KAAK,EAAE;YAAW,CAAE;YAAAY,QAAA,GAAEsL,IAAI,CAAC9H,QAAQ,EAAC,UAAQ,EAAC,IAAIhB,IAAI,CAAC8I,IAAI,CAAC/I,IAAI,CAAC,CAAC0G,kBAAkB,CAAC,OAAO,EAAE;cAAEC,GAAG,EAAE,SAAS;cAAEC,KAAK,EAAE,MAAM;cAAEC,IAAI,EAAE;YAAU,CAAC,CAAC;UAAA;YAAAlJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAClMpC,OAAA,CAAC5B,UAAU;YAACiE,OAAO,EAAC,OAAO;YAAC1B,EAAE,EAAE;cAAEQ,KAAK,EAAE;YAAW,CAAE;YAAAY,QAAA,EACnDsL,IAAI,CAAC1D,WAAW,CAAC9G,MAAM,GAAG,EAAE,GAAGwK,IAAI,CAAC1D,WAAW,CAACoB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGsC,IAAI,CAAC1D;UAAW;YAAA1H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC;QAAA,GALLiL,IAAI,CAACnH,EAAE;UAAAjE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMZ,CACN;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eACDpC,OAAA,CAACX,MAAM;MACLwM,IAAI,EAAEqC,eAAgB;MACtBxN,OAAO,EAAEA,CAAA,KAAMyN,kBAAkB,CAAC,KAAK,CAAE;MACzCwI,UAAU,EAAElH,QAAS;MACrBmH,mBAAmB,EAAEnX,KAAM;MAC3BoX,eAAe,EAAE;QAAE5K,SAAS,EAAE;MAAO,CAAE;MACvCtL,EAAE,EAAE;QAAEM,MAAM,EAAE;MAAK,CAAE;MACrB6V,UAAU,EAAE;QACVnW,EAAE,EAAE;UACF2I,EAAE,EAAE,CAAC;UACLhI,YAAY,EAAE;YAAER,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE,CAAC;UAC9BwC,KAAK,EAAE;YAAEzC,EAAE,EAAE,OAAO;YAAEC,EAAE,EAAE;UAAI,CAAC;UAC/BqI,EAAE,EAAE;YAAEtI,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAO,CAAC;UACzBF,GAAG,EAAE,CAAC;UACND,QAAQ,EAAE;QACZ;MACF,CAAE;MAAAmB,QAAA,eAEF/B,OAAA,CAAC3B,GAAG;QAACsC,EAAE,EAAE;UAAEwC,CAAC,EAAE,CAAC;UAAEuG,EAAE,EAAE,CAAC;UAAE9I,QAAQ,EAAE,UAAU;UAAEM,OAAO,EAAE,MAAM;UAAEoK,SAAS,EAAE;QAAI,CAAE;QAAAvJ,QAAA,gBAC9E/B,OAAA,CAACV,UAAU;UACTyE,OAAO,EAAEA,CAAA,KAAMoK,kBAAkB,CAAC,KAAK,CAAE;UACzCxN,EAAE,EAAE;YAAEC,QAAQ,EAAE,UAAU;YAAEC,GAAG,EAAE,EAAE;YAAEG,KAAK,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG,CAAE;UAC7D,cAAW,OAAO;UAAAc,QAAA,eAElB/B,OAAA,CAACR,SAAS;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACbpC,OAAA,CAACN,SAAS;UACR6W,SAAS;UACTvK,SAAS;UACT0K,WAAW,EAAC,wBAAwB;UACpCrU,OAAO,EAAC,UAAU;UAClB1B,EAAE,EAAE;YAAEqB,QAAQ,EAAE;UAAG;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA,eACT,CAAC;AAEP;AAACqL,GAAA,CAtwBuBD,WAAW;EAAA,QAChBxP,WAAW,EAgCdiB,QAAQ,EACJD,aAAa,EACdA,aAAa;AAAA;AAAA+X,GAAA,GAnCRvJ,WAAW;AAAA,IAAAjL,EAAA,EAAA0B,GAAA,EAAAkC,GAAA,EAAAmC,GAAA,EAAAwB,GAAA,EAAAoD,GAAA,EAAAK,GAAA,EAAAwJ,GAAA;AAAAC,YAAA,CAAAzU,EAAA;AAAAyU,YAAA,CAAA/S,GAAA;AAAA+S,YAAA,CAAA7Q,GAAA;AAAA6Q,YAAA,CAAA1O,GAAA;AAAA0O,YAAA,CAAAlN,GAAA;AAAAkN,YAAA,CAAA9J,GAAA;AAAA8J,YAAA,CAAAzJ,GAAA;AAAAyJ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}