<?php
// Disable error display to prevent HTML in JSON response
ini_set('display_errors', 0);
error_reporting(0);

// Set content type first
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Function to send JSON response and exit
function sendJsonResponse($data) {
    echo json_encode($data);
    exit;
}
// Helper function to generate slug
function generateSlug($text) {
    // Handle empty or null text
    if (empty($text)) {
        $text = 'untitled-post';
    }

    // Replace non-alphanumeric characters with hyphens
    $slug = preg_replace('/[^a-zA-Z0-9\s]/', '', $text);
    $slug = preg_replace('/\s+/', '-', trim($slug));
    $slug = strtolower($slug);

    // If slug is empty after processing, use default
    if (empty($slug)) {
        $slug = 'untitled-post';
    }

    // Ensure uniqueness
    $pdo = getConnection();
    $originalSlug = $slug;
    $counter = 1;

    while (true) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM posts WHERE slug = ?");
        $stmt->execute([$slug]);
        if ($stmt->fetchColumn() == 0) {
            break;
        }
        $slug = $originalSlug . '-' . $counter;
        $counter++;
    }

    return $slug;
}





// Function to add news with complete fields
function addNewsComplete($title, $content, $status, $image, $category_id, $image_alt = '', $excerpt = '', $meta_title = '', $meta_description = '', $tags = '', $featured = 0) {
    try {
        $pdo = getConnection();

        // Generate slug from title
        $slug = generateSlug($title);

        // Generate excerpt from content if not provided
        if (empty($excerpt)) {
            $excerpt = substr(strip_tags($content), 0, 200) . '...';
        }

        // Use title as meta_title if not provided
        if (empty($meta_title)) {
            $meta_title = $title;
        }

        // Generate meta_description from excerpt if not provided
        if (empty($meta_description)) {
            $meta_description = $excerpt;
        }

        // Calculate reading time (average 200 words per minute)
        $wordCount = str_word_count(strip_tags($content));
        $readingTime = max(1, ceil($wordCount / 200));

        // Set published_at if status is published
        $publishedAt = ($status === 'published') ? date('Y-m-d H:i:s') : null;

        $stmt = $pdo->prepare("
            INSERT INTO posts (
                title, slug, description, content, excerpt, image, image_alt,
                category_id, status, featured, meta_title, meta_description, tags,
                reading_time, published_at, date, user_id, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), 1, NOW(), NOW())
        ");

        return $stmt->execute([
            $title, $slug, $excerpt, $content, $excerpt, $image, $image_alt,
            $category_id, $status, $featured, $meta_title, $meta_description, $tags,
            $readingTime, $publishedAt
        ]);
    } catch (Exception $e) {
        error_log("Error in addNewsComplete: " . $e->getMessage());
        return false;
    }
}

// Function to add news (legacy compatibility)
function addNews($title, $content, $status, $image, $category_id) {
    return addNewsComplete($title, $content, $status, $image, $category_id);
}

// Function to update news with complete fields
function updateNewsComplete($id, $title, $content, $status, $image = null, $category_id = null, $image_alt = '', $excerpt = '', $meta_title = '', $meta_description = '', $tags = '', $featured = 0) {
    try {
        $pdo = getConnection();

        // Get current news data
        $currentNews = getNewsById($id);
        if (!$currentNews) {
            return false;
        }

        // Generate new slug if title changed
        $slug = ($currentNews['title'] !== $title) ? generateSlug($title) : $currentNews['slug'];

        // Use provided excerpt or generate from content
        if (empty($excerpt)) {
            $excerpt = substr(strip_tags($content), 0, 200) . '...';
        }

        // Calculate reading time
        $wordCount = str_word_count(strip_tags($content));
        $readingTime = max(1, ceil($wordCount / 200));

        // Set published_at if status changed to published
        $publishedAt = null;
        if ($status === 'published' && $currentNews['status'] !== 'published') {
            $publishedAt = date('Y-m-d H:i:s');
        } elseif ($currentNews['published_at']) {
            $publishedAt = $currentNews['published_at'];
        }

        // Use current values if not provided
        if ($category_id === null) {
            $category_id = $currentNews['category_id'];
        }
        if (empty($meta_title)) {
            $meta_title = $currentNews['meta_title'] ?? $title;
        }
        if (empty($meta_description)) {
            $meta_description = $currentNews['meta_description'] ?? $excerpt;
        }

        // Prepare SQL based on whether image is being updated
        if ($image) {
            $stmt = $pdo->prepare("
                UPDATE posts
                SET title = ?, slug = ?, description = ?, content = ?, excerpt = ?,
                    image = ?, image_alt = ?, category_id = ?, status = ?, featured = ?,
                    meta_title = ?, meta_description = ?, tags = ?, reading_time = ?,
                    published_at = ?, updated_at = NOW()
                WHERE id = ?
            ");
            return $stmt->execute([
                $title, $slug, $excerpt, $content, $excerpt,
                $image, $image_alt, $category_id, $status, $featured,
                $meta_title, $meta_description, $tags, $readingTime,
                $publishedAt, $id
            ]);
        } else {
            // Keep current image and image_alt if not updating
            $currentImage = $currentNews['image'];
            $currentImageAlt = empty($image_alt) ? $currentNews['image_alt'] : $image_alt;

            $stmt = $pdo->prepare("
                UPDATE posts
                SET title = ?, slug = ?, description = ?, content = ?, excerpt = ?,
                    image_alt = ?, category_id = ?, status = ?, featured = ?,
                    meta_title = ?, meta_description = ?, tags = ?, reading_time = ?,
                    published_at = ?, updated_at = NOW()
                WHERE id = ?
            ");
            return $stmt->execute([
                $title, $slug, $excerpt, $content, $excerpt,
                $currentImageAlt, $category_id, $status, $featured,
                $meta_title, $meta_description, $tags, $readingTime,
                $publishedAt, $id
            ]);
        }
    } catch (Exception $e) {
        error_log("Error in updateNewsComplete: " . $e->getMessage());
        return false;
    }
}

// Function to update news (legacy compatibility)
function updateNews($id, $title, $content, $status, $image = null, $category_id = null) {
    return updateNewsComplete($id, $title, $content, $status, $image, $category_id);
}

// Function to delete news
function deleteNews($id) {
    try {
        $pdo = getConnection();

        // First delete from saved table
        $stmt = $pdo->prepare("DELETE FROM saved WHERE post_id = ?");
        $stmt->execute([$id]);

        // Then delete the post
        $stmt = $pdo->prepare("DELETE FROM posts WHERE id = ?");
        return $stmt->execute([$id]);
    } catch (Exception $e) {
        error_log("Error in deleteNews: " . $e->getMessage());
        return false;
    }
}

// Function to handle logo file upload
function handleLogoUpload($file) {
    // Create uploads directory in public accessible location
    $uploadDir = $_SERVER['DOCUMENT_ROOT'] . '/react-news/uploads/';
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            return ['success' => false, 'message' => 'Failed to create upload directory'];
        }
    }

    // Validate file type
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    $fileType = $file['type'];
    if (!in_array($fileType, $allowedTypes)) {
        return ['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, GIF, and WebP are allowed.'];
    }

    // Validate file size (2MB max)
    if ($file['size'] > 2 * 1024 * 1024) {
        return ['success' => false, 'message' => 'File size too large. Maximum 2MB allowed.'];
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'logo_' . time() . '_' . uniqid() . '.' . $extension;
    $filePath = $uploadDir . $filename;

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        // Return web-accessible path for database storage
        $webPath = '/react-news/uploads/' . $filename;
        return [
            'success' => true,
            'path' => $webPath,
            'full_path' => $filePath,
            'filename' => $filename,
            'url' => 'http://localhost' . $webPath
        ];
    } else {
        return ['success' => false, 'message' => 'Failed to move uploaded file'];
    }
}

function handleNewsImageUpload($file) {
    // Create uploads directory in public accessible location
    $uploadDir = $_SERVER['DOCUMENT_ROOT'] . '/react-news/uploads/';
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            return ['success' => false, 'message' => 'Failed to create upload directory'];
        }
    }

    // Validate file type
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    $fileType = $file['type'];
    if (!in_array($fileType, $allowedTypes)) {
        return ['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, GIF, and WebP are allowed.'];
    }

    // Validate file size (5MB max for news images)
    if ($file['size'] > 5 * 1024 * 1024) {
        return ['success' => false, 'message' => 'File size too large. Maximum 5MB allowed.'];
    }

    // Generate unique filename for news
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'news_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
    $filePath = $uploadDir . $filename;

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        // Return web-accessible path for database storage
        $webPath = '/react-news/uploads/' . $filename;
        return [
            'success' => true,
            'path' => $webPath,
            'full_path' => $filePath,
            'filename' => $filename,
            'url' => 'http://localhost' . $webPath
        ];
    } else {
        return ['success' => false, 'message' => 'Failed to move uploaded file'];
    }
}

// Try to include config and handle errors
try {
    require_once __DIR__ . '/config.php';
} catch (Exception $e) {
    sendJsonResponse(['success' => false, 'message' => 'Database connection failed: ' . $e->getMessage()]);
}

// Get action from POST, GET, or JSON body
$action = $_POST['action'] ?? $_GET['action'] ?? '';

// If no action found in POST/GET, try JSON body
if (empty($action)) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
}

// Debug logging (remove in production)
error_log("API Action: " . $action);
error_log("POST data: " . print_r($_POST, true));
error_log("JSON input: " . file_get_contents('php://input'));

// Create uploads directory if it doesn't exist
$uploadsDir = __DIR__ . '/assets/news';
if (!is_dir($uploadsDir)) {
    mkdir($uploadsDir, 0755, true);
}

// Wrap everything in try-catch to handle errors properly
try {
    switch ($action) {
        case 'get_stats':
            $stats = getStats();
            sendJsonResponse(['success' => true, 'data' => $stats]);
            break;

        case 'get_news':
            $limit = $_GET['limit'] ?? null;
            $category = $_GET['category'] ?? null;
            $result = getNews($limit, $category);
            sendJsonResponse($result);
            break;

        case 'add_news':
            $title = $_POST['title'] ?? '';
            $content = $_POST['content'] ?? '';
            $status = $_POST['status'] ?? 'draft';
            $category_id = $_POST['category_id'] ?? 1;
            $image_alt = $_POST['image_alt'] ?? '';
            $excerpt = $_POST['excerpt'] ?? '';
            $meta_title = $_POST['meta_title'] ?? '';
            $meta_description = $_POST['meta_description'] ?? '';
            $tags = $_POST['tags'] ?? '';
            $featured = isset($_POST['featured']) && $_POST['featured'] == '1' ? 1 : 0;
            $image = null;

            if (empty($title) || empty($content)) {
                sendJsonResponse(['success' => false, 'message' => 'Title and content are required']);
            }

            // Handle image upload
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $file = $_FILES['image'];

                // Validate file type
                $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
                if (!in_array($file['type'], $allowedTypes)) {
                    sendJsonResponse(['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, GIF, and WebP are allowed.']);
                }

                // Validate file size (5MB max for news images)
                if ($file['size'] > 5 * 1024 * 1024) {
                    sendJsonResponse(['success' => false, 'message' => 'File size too large. Maximum 5MB allowed.']);
                }

                // Use the same upload handler as logo
                $uploadResult = handleNewsImageUpload($file);
                if ($uploadResult['success']) {
                    $image = $uploadResult['path'];
                } else {
                    sendJsonResponse(['success' => false, 'message' => $uploadResult['message']]);
                }
            }

            $result = addNewsComplete($title, $content, $status, $image, $category_id, $image_alt, $excerpt, $meta_title, $meta_description, $tags, $featured);
            sendJsonResponse(['success' => $result, 'message' => $result ? 'News added successfully' : 'Failed to add news']);
            break;

        case 'update_news':
            $id = $_POST['id'] ?? 0;
            $title = $_POST['title'] ?? '';
            $content = $_POST['content'] ?? '';
            $status = $_POST['status'] ?? 'draft';
            $category_id = $_POST['category_id'] ?? null;
            $image_alt = $_POST['image_alt'] ?? '';
            $excerpt = $_POST['excerpt'] ?? '';
            $meta_title = $_POST['meta_title'] ?? '';
            $meta_description = $_POST['meta_description'] ?? '';
            $tags = $_POST['tags'] ?? '';
            $featured = isset($_POST['featured']) && $_POST['featured'] == '1' ? 1 : 0;
            $image = null;

            if (empty($id) || empty($title) || empty($content)) {
                sendJsonResponse(['success' => false, 'message' => 'ID, title and content are required']);
            }

            // Handle image upload
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $file = $_FILES['image'];

                // Use the same upload handler as add_news
                $uploadResult = handleNewsImageUpload($file);
                if ($uploadResult['success']) {
                    $image = $uploadResult['path'];

                    // Delete old image if exists
                    $oldNews = getNewsById($id);
                    if ($oldNews && $oldNews['image']) {
                        // Handle both old and new path formats
                        $oldImagePath = '';
                        if (strpos($oldNews['image'], '/react-news/uploads/') === 0) {
                            $oldImagePath = $_SERVER['DOCUMENT_ROOT'] . $oldNews['image'];
                        } else if (strpos($oldNews['image'], 'assets/') === 0) {
                            $oldImagePath = __DIR__ . '/' . $oldNews['image'];
                        }

                        if ($oldImagePath && file_exists($oldImagePath)) {
                            unlink($oldImagePath);
                        }
                    }
                } else {
                    sendJsonResponse(['success' => false, 'message' => $uploadResult['message']]);
                }
            }

            $result = updateNewsComplete($id, $title, $content, $status, $image, $category_id, $image_alt, $excerpt, $meta_title, $meta_description, $tags, $featured);
            sendJsonResponse(['success' => $result, 'message' => $result ? 'News updated successfully' : 'Failed to update news']);
            break;

        case 'delete_news':
            $id = $_POST['id'] ?? 0;

            if (empty($id)) {
                sendJsonResponse(['success' => false, 'message' => 'ID is required']);
            }

            $result = deleteNews($id);
            sendJsonResponse(['success' => $result, 'message' => $result ? 'News deleted successfully' : 'Failed to delete news']);
            break;

        case 'get_news_by_id':
            $id = $_GET['id'] ?? 0;

            if (empty($id)) {
                sendJsonResponse(['success' => false, 'message' => 'ID is required']);
            }

            $news = getNewsById($id);
            sendJsonResponse(['success' => $news !== false, 'data' => $news]);
            break;

        case 'get_saved_news':
            $savedNews = getSavedNews();
            sendJsonResponse(['success' => true, 'data' => $savedNews]);
            break;

        case 'add_saved_news':
            $input = json_decode(file_get_contents('php://input'), true);
            $postId = $input['post_id'] ?? 0;
            if (empty($postId)) {
                sendJsonResponse(['success' => false, 'message' => 'Post ID is required']);
            }

            $result = addSavedNews($postId);
            sendJsonResponse(['success' => $result, 'message' => $result ? 'News saved successfully' : 'Failed to save news']);
            break;

        case 'remove_saved_news':
            $input = json_decode(file_get_contents('php://input'), true);
            $postId = $input['post_id'] ?? 0;
            if (empty($postId)) {
                sendJsonResponse(['success' => false, 'message' => 'Post ID is required']);
            }

            $result = removeSavedNews($postId);
            sendJsonResponse(['success' => $result, 'message' => $result ? 'News removed from saved successfully' : 'Failed to remove news from saved']);
            break;

        case 'update_website_settings':
            $websiteName = $_POST['website_name'] ?? '';
            $websiteDescription = $_POST['website_description'] ?? '';
            $success = true;
            $messages = [];
            $logoPath = '';

            // Handle logo file upload
            if (isset($_FILES['logo_file']) && $_FILES['logo_file']['error'] === UPLOAD_ERR_OK) {
                $uploadResult = handleLogoUpload($_FILES['logo_file']);
                if ($uploadResult['success']) {
                    $logoPath = $uploadResult['path'];
                    if (!updateSetting('website_logo', $logoPath)) {
                        $success = false;
                        $messages[] = 'Failed to save logo path to database';
                    }
                } else {
                    $success = false;
                    $messages[] = $uploadResult['message'];
                }
            }

            // Update website name
            if (!empty($websiteName)) {
                if (!updateSetting('website_name', $websiteName)) {
                    $success = false;
                    $messages[] = 'Failed to update website name';
                }
            }

            // Update website description
            if (!empty($websiteDescription)) {
                if (!updateSetting('website_description', $websiteDescription)) {
                    $success = false;
                    $messages[] = 'Failed to update website description';
                }
            }

            sendJsonResponse([
                'success' => $success,
                'message' => $success ? 'Website settings updated successfully' : implode(', ', $messages),
                'data' => [
                    'website_name' => $websiteName,
                    'website_logo' => $logoPath ?: getSetting('website_logo'),
                    'website_description' => $websiteDescription
                ]
            ]);
            break;

        case 'update_color_settings':
            $primaryColor = $_POST['primary_color'] ?? '';
            $secondaryColor = $_POST['secondary_color'] ?? '';
            $accentColor = $_POST['accent_color'] ?? '';
            $success = true;
            $messages = [];

            if (!empty($primaryColor)) {
                if (!updateSetting('primary_color', $primaryColor)) {
                    $success = false;
                    $messages[] = 'Failed to update primary color';
                }
            }

            if (!empty($secondaryColor)) {
                if (!updateSetting('secondary_color', $secondaryColor)) {
                    $success = false;
                    $messages[] = 'Failed to update secondary color';
                }
            }

            if (!empty($accentColor)) {
                if (!updateSetting('accent_color', $accentColor)) {
                    $success = false;
                    $messages[] = 'Failed to update accent color';
                }
            }

            sendJsonResponse([
                'success' => $success,
                'message' => $success ? 'Color settings updated successfully' : implode(', ', $messages)
            ]);
            break;

        case 'get_settings':
            $settings = [
                'website_name' => getSetting('website_name'),
                'website_logo' => getSetting('website_logo'),
                'primary_color' => getSetting('primary_color'),
                'secondary_color' => getSetting('secondary_color'),
                'accent_color' => getSetting('accent_color')
            ];
            sendJsonResponse(['success' => true, 'data' => $settings]);
            break;

        case 'upload_logo':
            if (!isset($_FILES['logo']) || $_FILES['logo']['error'] !== UPLOAD_ERR_OK) {
                sendJsonResponse(['success' => false, 'message' => 'No file uploaded or upload error']);
            }

            $file = $_FILES['logo'];

            // Validate file type
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($file['type'], $allowedTypes)) {
                sendJsonResponse(['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, GIF, and WebP are allowed.']);
            }

            // Validate file size (2MB max)
            if ($file['size'] > 2 * 1024 * 1024) {
                sendJsonResponse(['success' => false, 'message' => 'File size too large. Maximum 2MB allowed.']);
            }

            // Create assets directory if it doesn't exist
            $assetsDir = __DIR__ . '/assets';
            if (!is_dir($assetsDir)) {
                mkdir($assetsDir, 0755, true);
            }

            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'logo_' . time() . '.' . $extension;
            $filepath = 'assets/' . $filename;

            if (move_uploaded_file($file['tmp_name'], __DIR__ . '/' . $filepath)) {
                updateSetting('website_logo', $filepath);
                sendJsonResponse(['success' => true, 'message' => 'Logo uploaded successfully', 'filepath' => $filepath]);
            } else {
                sendJsonResponse(['success' => false, 'message' => 'Failed to upload logo']);
            }
            break;

        case 'get_categories':
            try {
                $categories = getCategories();
                if ($categories === false) {
                    sendJsonResponse(['success' => false, 'message' => 'Failed to fetch categories']);
                } else {
                    sendJsonResponse(['success' => true, 'data' => $categories]);
                }
            } catch (Exception $e) {
                error_log("Error in get_categories API: " . $e->getMessage());
                sendJsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
            }
            break;

        case 'get_recent_activities':
            $limit = $_GET['limit'] ?? 10;
            $activities = getRecentActivities($limit);
            sendJsonResponse(['success' => true, 'data' => $activities]);
            break;

        case 'get_popular_posts':
            $limit = $_GET['limit'] ?? 5;
            $posts = getPopularPosts($limit);
            sendJsonResponse(['success' => true, 'data' => $posts]);
            break;

        case 'get_dashboard_data':
            $stats = getStats();
            $recentActivities = getRecentActivities(5);
            $popularPosts = getPopularPosts(5);
            $categories = getCategories();

            sendJsonResponse([
                'success' => true,
                'data' => [
                    'stats' => $stats,
                    'recent_activities' => $recentActivities,
                    'popular_posts' => $popularPosts,
                    'categories' => $categories
                ]
            ]);
            break;

        case 'get_saved_posts':
            $pdo = getConnection();
            $stmt = $pdo->query("
                SELECT
                    p.id, p.title, p.description as content, p.image,
                    p.views, p.share, p.likes, p.created_at,
                    c.name as category_name, c.color as category_color,
                    a.username as author_name
                FROM saved s
                JOIN posts p ON s.post_id = p.id
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN admin a ON p.user_id = a.id
                ORDER BY s.saved_at DESC
            ");
            sendJsonResponse($stmt->fetchAll());
            break;

        case 'save_post':
            $postId = $_POST['post_id'] ?? $_GET['post_id'] ?? 0;
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';

            if (empty($postId)) {
                sendJsonResponse(['success' => false, 'message' => 'Post ID is required']);
            }

            $pdo = getConnection();
            try {
                $stmt = $pdo->prepare("INSERT IGNORE INTO saved (post_id, ip_address) VALUES (?, ?)");
                $result = $stmt->execute([$postId, $ipAddress]);
                sendJsonResponse(['success' => $result, 'message' => $result ? 'Post saved successfully' : 'Post already saved']);
            } catch (Exception $e) {
                sendJsonResponse(['success' => false, 'message' => 'Error saving post: ' . $e->getMessage()]);
            }
            break;

        case 'unsave_post':
            $postId = $_POST['post_id'] ?? $_GET['post_id'] ?? 0;
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';

            if (empty($postId)) {
                sendJsonResponse(['success' => false, 'message' => 'Post ID is required']);
            }

            $pdo = getConnection();
            $stmt = $pdo->prepare("DELETE FROM saved WHERE post_id = ? AND ip_address = ?");
            $result = $stmt->execute([$postId, $ipAddress]);
            sendJsonResponse(['success' => $result, 'message' => $result ? 'Post unsaved successfully' : 'Failed to unsave post']);
            break;

        case 'update_post_share':
            $postId = $_POST['post_id'] ?? $_GET['post_id'] ?? 0;

            if (empty($postId)) {
                sendJsonResponse(['success' => false, 'message' => 'Post ID is required']);
            }

            $pdo = getConnection();
            $stmt = $pdo->prepare("UPDATE posts SET share = share + 1 WHERE id = ?");
            $result = $stmt->execute([$postId]);
            sendJsonResponse(['success' => $result, 'message' => $result ? 'Share count updated' : 'Failed to update share count']);
            break;

        case 'update_post_views':
            $postId = $_POST['post_id'] ?? $_GET['post_id'] ?? 0;

            if (empty($postId)) {
                sendJsonResponse(['success' => false, 'message' => 'Post ID is required']);
            }

            $pdo = getConnection();
            $stmt = $pdo->prepare("UPDATE posts SET views = views + 1 WHERE id = ?");
            $result = $stmt->execute([$postId]);
            sendJsonResponse(['success' => $result, 'message' => $result ? 'View count updated' : 'Failed to update view count']);
            break;

        case 'increment_views':
            $postId = $_POST['id'] ?? $_GET['id'] ?? 0;

            if (empty($postId)) {
                sendJsonResponse(['success' => false, 'message' => 'Post ID is required']);
            }

            $pdo = getConnection();
            $stmt = $pdo->prepare("UPDATE posts SET views = views + 1 WHERE id = ?");
            $result = $stmt->execute([$postId]);
            sendJsonResponse(['success' => $result, 'message' => $result ? 'View count updated' : 'Failed to update view count']);
            break;

        default:
            sendJsonResponse(['success' => false, 'message' => 'Invalid action: ' . $action, 'received_action' => $action]);
            break;
    }
} catch (Exception $e) {
    sendJsonResponse(['success' => false, 'message' => 'An error occurred: ' . $e->getMessage()]);
}

// Function to get saved news from database
function getSavedNews() {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("
            SELECT p.*, c.name as category_name
            FROM saved s
            JOIN posts p ON s.post_id = p.id
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.status = 'published'
            ORDER BY s.created_at DESC
        ");
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Format the results to match frontend expectations
        $formattedResults = [];
        foreach ($results as $row) {
            $formattedResults[] = [
                'id' => (int)$row['id'],
                'title' => $row['title'],
                'description' => $row['content'], // Use content as description
                'content' => $row['content'],
                'image' => $row['image'],
                'category' => $row['category_name'] ?: 'Umum',
                'category_name' => $row['category_name'] ?: 'Umum',
                'category_id' => (int)$row['category_id'],
                'status' => $row['status'],
                'views' => (int)$row['views'],
                'date' => $row['created_at'],
                'created_at' => $row['created_at'],
                'updated_at' => $row['updated_at']
            ];
        }

        return $formattedResults;
    } catch (Exception $e) {
        error_log("Error in getSavedNews: " . $e->getMessage());
        return [];
    }
}

// Function to get news by ID
function getNewsById($id) {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("
            SELECT
                p.id,
                p.title,
                p.slug,
                p.description,
                p.content,
                p.excerpt,
                p.image,
                p.image_alt,
                p.category_id,
                p.status,
                p.featured,
                p.meta_title,
                p.meta_description,
                p.tags,
                p.share,
                p.views,
                p.likes,
                p.comments_count,
                p.reading_time,
                p.published_at,
                p.date,
                p.user_id,
                p.created_at,
                p.updated_at,
                c.name as category_name,
                c.color as category_color
            FROM posts p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.id = ?
        ");
        $stmt->execute([$id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result) {
            return [
                'id' => (int)$result['id'],
                'title' => $result['title'],
                'slug' => $result['slug'],
                'description' => $result['description'] ?: ($result['excerpt'] ?: substr(strip_tags($result['content']), 0, 200) . '...'),
                'content' => $result['content'],
                'excerpt' => $result['excerpt'],
                'image' => $result['image'],
                'image_alt' => $result['image_alt'],
                'category' => $result['category_name'] ?: 'Umum',
                'category_name' => $result['category_name'] ?: 'Umum',
                'category_id' => (int)$result['category_id'],
                'category_color' => $result['category_color'] ?: '#3B82F6',
                'status' => $result['status'],
                'featured' => (bool)$result['featured'],
                'meta_title' => $result['meta_title'],
                'meta_description' => $result['meta_description'],
                'tags' => $result['tags'],
                'share' => (int)$result['share'],
                'views' => (int)$result['views'],
                'likes' => (int)$result['likes'],
                'comments_count' => (int)$result['comments_count'],
                'reading_time' => (int)$result['reading_time'],
                'published_at' => $result['published_at'],
                'date' => $result['date'] ?: $result['created_at'],
                'user_id' => (int)$result['user_id'],
                'created_at' => $result['created_at'],
                'updated_at' => $result['updated_at']
            ];
        }

        return false;
    } catch (Exception $e) {
        error_log("Error in getNewsById: " . $e->getMessage());
        return false;
    }
}

// Function to get all news with optional limit and category filter
function getNews($limit = null, $category = null) {
    try {
        $pdo = getConnection();

        $sql = "
            SELECT
                p.id,
                p.title,
                p.slug,
                p.description,
                p.content,
                p.excerpt,
                p.image,
                p.image_alt,
                p.category_id,
                p.status,
                p.featured,
                p.meta_title,
                p.meta_description,
                p.tags,
                p.share,
                p.views,
                p.likes,
                p.comments_count,
                p.reading_time,
                p.published_at,
                p.date,
                p.user_id,
                p.created_at,
                p.updated_at,
                c.name as category_name,
                c.color as category_color
            FROM posts p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE 1=1
        ";

        $params = [];

        // Add category filter if specified
        if ($category && $category !== 'all') {
            $sql .= " AND p.category_id = ?";
            $params[] = $category;
        }

        $sql .= " ORDER BY p.created_at DESC";

        if ($limit) {
            $sql .= " LIMIT " . (int)$limit;
        }

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Format the results to match frontend expectations
        $formattedResults = [];
        foreach ($results as $row) {
            $formattedResults[] = [
                'id' => (int)$row['id'],
                'title' => $row['title'],
                'slug' => $row['slug'],
                'description' => $row['description'] ?: ($row['excerpt'] ?: substr(strip_tags($row['content']), 0, 200) . '...'),
                'content' => $row['content'],
                'excerpt' => $row['excerpt'],
                'image' => $row['image'],
                'image_alt' => $row['image_alt'],
                'category' => $row['category_name'] ?: 'Umum',
                'category_name' => $row['category_name'] ?: 'Umum',
                'category_id' => (int)$row['category_id'],
                'category_color' => $row['category_color'] ?: '#3B82F6',
                'status' => $row['status'],
                'featured' => (bool)$row['featured'],
                'meta_title' => $row['meta_title'],
                'meta_description' => $row['meta_description'],
                'tags' => $row['tags'],
                'share' => (int)$row['share'],
                'views' => (int)$row['views'],
                'likes' => (int)$row['likes'],
                'comments_count' => (int)$row['comments_count'],
                'reading_time' => (int)$row['reading_time'],
                'published_at' => $row['published_at'],
                'date' => $row['date'] ?: $row['created_at'],
                'user_id' => (int)$row['user_id'],
                'created_at' => $row['created_at'],
                'updated_at' => $row['updated_at']
            ];
        }

        return ['success' => true, 'data' => $formattedResults];
    } catch (Exception $e) {
        error_log("Error in getNews: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to fetch news', 'error' => $e->getMessage()];
    }
}

// Function to add news to saved/bookmark
function addSavedNews($postId) {
    try {
        $pdo = getConnection();

        // Check if already saved
        $checkStmt = $pdo->prepare("SELECT id FROM saved WHERE post_id = ?");
        $checkStmt->execute([$postId]);
        if ($checkStmt->fetch()) {
            return true; // Already saved
        }

        // Add to saved
        $stmt = $pdo->prepare("INSERT INTO saved (post_id, created_at) VALUES (?, NOW())");
        return $stmt->execute([$postId]);
    } catch (Exception $e) {
        error_log("Error in addSavedNews: " . $e->getMessage());
        return false;
    }
}

// Function to remove news from saved/bookmark
function removeSavedNews($postId) {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("DELETE FROM saved WHERE post_id = ?");
        return $stmt->execute([$postId]);
    } catch (Exception $e) {
        error_log("Error in removeSavedNews: " . $e->getMessage());
        return false;
    }
}
?>
