<?php
// Test update news functionality
require_once 'config.php';

header('Content-Type: application/json');

try {
    // Test if updateNewsComplete function exists
    if (!function_exists('updateNewsComplete')) {
        echo "✗ updateNewsComplete function does not exist\n";
        exit;
    }
    echo "✓ updateNewsComplete function exists\n";
    
    // Check if we have any news to test with
    $pdo = getConnection();
    $stmt = $pdo->query("SELECT * FROM posts LIMIT 1");
    $testNews = $stmt->fetch();
    
    if (!$testNews) {
        echo "⚠ No news found in database to test with\n";
        exit;
    }
    
    echo "✓ Found test news: ID " . $testNews['id'] . " - " . $testNews['title'] . "\n";
    
    // Test the function with minimal parameters
    $result = updateNewsComplete(
        $testNews['id'],
        $testNews['title'] . ' (Updated)',
        $testNews['content'],
        $testNews['status']
    );
    
    if ($result) {
        echo "✓ updateNewsComplete test successful\n";
        
        // Revert the change
        $revertResult = updateNewsComplete(
            $testNews['id'],
            $testNews['title'],
            $testNews['content'],
            $testNews['status']
        );
        
        if ($revertResult) {
            echo "✓ Test reverted successfully\n";
        } else {
            echo "⚠ Failed to revert test change\n";
        }
    } else {
        echo "✗ updateNewsComplete test failed\n";
    }
    
    echo "\n✓ All tests completed!\n";
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
